const mysql = require('mysql2/promise');

async function addShareImageField() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'root',
    database: 'ai_agent'
  });

  try {
    // 检查字段是否已存在
    const [columns] = await connection.execute(`
      SELECT COLUMN_NAME
      FROM INFORMATION_SCHEMA.COLUMNS
      WHERE TABLE_SCHEMA = 'ai_agent'
      AND TABLE_NAME = 'config_referral'
      AND COLUMN_NAME = 'share_image_url'
    `);

    if (columns.length === 0) {
      // 字段不存在，添加它
      await connection.execute(`
        ALTER TABLE config_referral 
        ADD COLUMN share_image_url varchar(500) NOT NULL DEFAULT '' COMMENT '分享图片URL' 
        AFTER share_description
      `);
      console.log('✅ 成功添加 share_image_url 字段');
    } else {
      console.log('ℹ️  share_image_url 字段已存在');
    }

    // 查看当前表结构
    const [tableStructure] = await connection.execute('DESCRIBE config_referral');
    console.log('\n当前表结构:');
    console.table(tableStructure);

  } catch (error) {
    console.error('❌ 执行失败:', error.message);
  } finally {
    await connection.end();
  }
}

addShareImageField();
