/**
 * 新的配置控制器 - 支持独立配置表
 */
import { Request, Response } from 'express';
import { AppDataSource } from '../data-source';
import { Not } from 'typeorm';
import { ConfigWechatOfficial } from '../entity/ConfigWechatOfficial';
import { ConfigWechatMiniprogram } from '../entity/ConfigWechatMiniprogram';
import { ConfigWechatPay } from '../entity/ConfigWechatPay';
import { ConfigAlipay } from '../entity/ConfigAlipay';
import { ConfigKouzi } from '../entity/ConfigKouzi';
import { ConfigVerification } from '../entity/ConfigVerification';
import { ConfigFreeQuota } from '../entity/ConfigFreeQuota';
import { ConfigReferral } from '../entity/ConfigReferral';
import { ConfigSystem } from '../entity/ConfigSystem';

/**
 * 获取微信公众号配置
 */
export const getWechatOfficialConfig = async (req: Request, res: Response) => {
  try {
    const repository = AppDataSource.getRepository(ConfigWechatOfficial);
    let config = await repository.findOne({ where: { id: 1 } });
    
    if (!config) {
      // 如果没有配置，创建默认配置
      config = repository.create({
        enabled: true, // 公众号配置默认启用
        appId: '',
        appSecret: '',
        token: '',
        encodingAesKey: ''
      });
      await repository.save(config);
    }
    
    return res.json({
      success: true,
      data: {
        configValue: config
      }
    });
  } catch (error: any) {
    console.error('获取微信公众号配置失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取配置失败',
      error: error.message
    });
  }
};

/**
 * 保存微信公众号配置
 */
export const saveWechatOfficialConfig = async (req: Request, res: Response) => {
  try {
    const repository = AppDataSource.getRepository(ConfigWechatOfficial);
    const { appId, appSecret, token, encodingAesKey, verifyFileName, verifyFileContent } = req.body;

    let config = await repository.findOne({ where: { id: 1 } });

    if (config) {
      // 更新现有配置
      config.enabled = true; // 公众号配置始终启用
      config.appId = appId || '';
      config.appSecret = appSecret || '';
      config.token = token || '';
      config.encodingAesKey = encodingAesKey || '';
      config.verifyFileName = verifyFileName || '';
      config.verifyFileContent = verifyFileContent || '';
    } else {
      // 创建新配置
      config = repository.create({
        enabled: true, // 公众号配置始终启用
        appId: appId || '',
        appSecret: appSecret || '',
        token: token || '',
        encodingAesKey: encodingAesKey || '',
        verifyFileName: verifyFileName || '',
        verifyFileContent: verifyFileContent || ''
      });
    }

    await repository.save(config);

    return res.json({
      success: true,
      message: '配置保存成功',
      data: config
    });
  } catch (error: any) {
    console.error('保存微信公众号配置失败:', error);
    return res.status(500).json({
      success: false,
      message: '保存配置失败',
      error: error.message
    });
  }
};

/**
 * 获取微信小程序配置
 */
export const getWechatMiniprogramConfig = async (req: Request, res: Response) => {
  try {
    const repository = AppDataSource.getRepository(ConfigWechatMiniprogram);
    let config = await repository.findOne({ where: { id: 1 } });
    
    if (!config) {
      config = repository.create({
        enabled: false,
        appId: '',
        appSecret: ''
      });
      await repository.save(config);
    }
    
    return res.json({
      success: true,
      data: {
        configValue: config
      }
    });
  } catch (error: any) {
    console.error('获取微信小程序配置失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取配置失败',
      error: error.message
    });
  }
};

/**
 * 保存微信小程序配置
 */
export const saveWechatMiniprogramConfig = async (req: Request, res: Response) => {
  try {
    const repository = AppDataSource.getRepository(ConfigWechatMiniprogram);
    const {
      enabled, appId, appSecret, verifyFileName, verifyFileContent,
      bannerAdEnabled, bannerAdId, interstitialAdEnabled, interstitialAdId,
      interstitialAdCount, interstitialAdInterval, videoAdEnabled, videoAdId
    } = req.body;

    let config = await repository.findOne({ where: { id: 1 } });

    if (config) {
      config.enabled = enabled;
      config.appId = appId || '';
      config.appSecret = appSecret || '';
      config.verifyFileName = verifyFileName || '';
      config.verifyFileContent = verifyFileContent || '';
      config.bannerAdEnabled = bannerAdEnabled || false;
      config.bannerAdId = bannerAdId || '';
      config.interstitialAdEnabled = interstitialAdEnabled || false;
      config.interstitialAdId = interstitialAdId || '';
      config.interstitialAdCount = interstitialAdCount || 0;
      config.interstitialAdInterval = interstitialAdInterval || 0;
      config.videoAdEnabled = videoAdEnabled || false;
      config.videoAdId = videoAdId || '';
    } else {
      config = repository.create({
        enabled: enabled || false,
        appId: appId || '',
        appSecret: appSecret || '',
        verifyFileName: verifyFileName || '',
        verifyFileContent: verifyFileContent || '',
        bannerAdEnabled: bannerAdEnabled || false,
        bannerAdId: bannerAdId || '',
        interstitialAdEnabled: interstitialAdEnabled || false,
        interstitialAdId: interstitialAdId || '',
        interstitialAdCount: interstitialAdCount || 0,
        interstitialAdInterval: interstitialAdInterval || 0,
        videoAdEnabled: videoAdEnabled || false,
        videoAdId: videoAdId || ''
      });
    }

    await repository.save(config);

    return res.json({
      success: true,
      message: '配置保存成功',
      data: config
    });
  } catch (error: any) {
    console.error('保存微信小程序配置失败:', error);
    return res.status(500).json({
      success: false,
      message: '保存配置失败',
      error: error.message
    });
  }
};

/**
 * 获取微信支付配置
 */
export const getWechatPayConfig = async (req: Request, res: Response) => {
  try {
    const repository = AppDataSource.getRepository(ConfigWechatPay);
    let config = await repository.findOne({ where: { id: 1 } });
    
    if (!config) {
      config = repository.create({
        enabled: false,
        sandbox: true,
        mchId: '',
        apiV3Key: '',
        apiKey: '',
        serialNo: '',
        certificateContent: '',
        privateKeyContent: '',
        notifyUrl: ''
      });
      await repository.save(config);
    }
    
    return res.json({
      success: true,
      data: config
    });
  } catch (error: any) {
    console.error('获取微信支付配置失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取配置失败',
      error: error.message
    });
  }
};

/**
 * 保存微信支付配置
 */
export const saveWechatPayConfig = async (req: Request, res: Response) => {
  try {
    const repository = AppDataSource.getRepository(ConfigWechatPay);
    const { 
      enabled, sandbox, mchId, apiV3Key, apiKey, serialNo, 
      certificateContent, privateKeyContent, notifyUrl 
    } = req.body;
    
    let config = await repository.findOne({ where: { id: 1 } });
    
    if (config) {
      config.enabled = enabled === true || enabled === 'true';
      config.sandbox = sandbox === true || sandbox === 'true';
      config.mchId = mchId || '';
      config.apiV3Key = apiV3Key || '';
      config.apiKey = apiKey || '';
      config.serialNo = serialNo || '';
      config.certificateContent = certificateContent || '';
      config.privateKeyContent = privateKeyContent || '';
      config.notifyUrl = notifyUrl || '';
    } else {
      config = repository.create({
        enabled: enabled === true || enabled === 'true',
        sandbox: sandbox === true || sandbox === 'true',
        mchId: mchId || '',
        apiV3Key: apiV3Key || '',
        apiKey: apiKey || '',
        serialNo: serialNo || '',
        certificateContent: certificateContent || '',
        privateKeyContent: privateKeyContent || '',
        notifyUrl: notifyUrl || ''
      });
    }
    
    await repository.save(config);
    
    return res.json({
      success: true,
      message: '配置保存成功',
      data: config
    });
  } catch (error: any) {
    console.error('保存微信支付配置失败:', error);
    return res.status(500).json({
      success: false,
      message: '保存配置失败',
      error: error.message
    });
  }
};

/**
 * 获取支付宝配置
 */
export const getAlipayConfig = async (req: Request, res: Response) => {
  try {
    const repository = AppDataSource.getRepository(ConfigAlipay);
    // 获取最新的配置记录
    let config = await repository.findOne({
      where: {},
      order: { id: 'DESC' }
    });

    if (!config) {
      config = repository.create({
        enabled: false,
        sandboxMode: true,
        appId: '',
        privateKey: '',
        alipayPublicKey: '',
        signType: 'RSA2',
        notifyUrl: '',
        enablePcPay: true,
        pcReturnUrl: '',
        enableWapPay: true,
        wapReturnUrl: '',
        wapQuitUrl: ''
      });
      await repository.save(config);
    }

    console.log('获取支付宝配置:', JSON.stringify(config, null, 2));

    return res.json({
      success: true,
      data: config
    });
  } catch (error: any) {
    console.error('获取支付宝配置失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取配置失败',
      error: error.message
    });
  }
};

/**
 * 保存支付宝配置
 */
export const saveAlipayConfig = async (req: Request, res: Response) => {
  try {
    const repository = AppDataSource.getRepository(ConfigAlipay);
    const {
      enabled, sandboxMode, appId, privateKey, alipayPublicKey, signType,
      notifyUrl, enablePcPay, pcReturnUrl, enableWapPay, wapReturnUrl, wapQuitUrl
    } = req.body;

    console.log('保存支付宝配置请求数据:', JSON.stringify(req.body, null, 2));

    // 获取最新的配置记录
    let config = await repository.findOne({
      where: {},
      order: { id: 'DESC' }
    });

    if (config) {
      // 正确处理布尔值转换
      config.enabled = enabled === true || enabled === 'true';
      config.sandboxMode = sandboxMode === true || sandboxMode === 'true';
      config.appId = appId || '';
      config.privateKey = privateKey || '';
      config.alipayPublicKey = alipayPublicKey || '';
      config.signType = signType || 'RSA2';
      config.notifyUrl = notifyUrl || '';
      config.enablePcPay = enablePcPay === true || enablePcPay === 'true';
      config.pcReturnUrl = pcReturnUrl || '';
      config.enableWapPay = enableWapPay === true || enableWapPay === 'true';
      config.wapReturnUrl = wapReturnUrl || '';
      config.wapQuitUrl = wapQuitUrl || '';
    } else {
      config = repository.create({
        enabled: enabled === true || enabled === 'true',
        sandboxMode: sandboxMode === true || sandboxMode === 'true',
        appId: appId || '',
        privateKey: privateKey || '',
        alipayPublicKey: alipayPublicKey || '',
        signType: signType || 'RSA2',
        notifyUrl: notifyUrl || '',
        enablePcPay: enablePcPay === true || enablePcPay === 'true',
        pcReturnUrl: pcReturnUrl || '',
        enableWapPay: enableWapPay === true || enableWapPay === 'true',
        wapReturnUrl: wapReturnUrl || '',
        wapQuitUrl: wapQuitUrl || ''
      });
    }

    await repository.save(config);

    console.log('支付宝配置保存成功:', JSON.stringify(config, null, 2));

    return res.json({
      success: true,
      message: '配置保存成功',
      data: config
    });
  } catch (error: any) {
    console.error('保存支付宝配置失败:', error);
    return res.status(500).json({
      success: false,
      message: '保存配置失败',
      error: error.message
    });
  }
};

/**
 * 获取扣子配置
 */
export const getKouziConfig = async (req: Request, res: Response) => {
  try {
    console.log('=== 新的扣子配置控制器被调用！重启后版本 ===');
    console.log('请求路径:', req.path);
    console.log('请求方法:', req.method);
    console.log('当前时间:', new Date().toISOString());
    const repository = AppDataSource.getRepository(ConfigKouzi);
    // 查询第一条记录（按 id 升序）
    let config = await repository.findOne({
      where: {},
      order: { id: 'ASC' }
    });
    console.log('查询到的配置记录:', config);

    if (!config) {
      config = repository.create({
        enabled: false,
        appId: '',
        authMode: 'jwt',
        privateKey: '',
        publicKey: '',
        personalAccessToken: ''
      });
      await repository.save(config);
    }

    return res.json({
      success: true,
      data: config
    });
  } catch (error: any) {
    console.error('获取扣子配置失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取配置失败',
      error: error.message
    });
  }
};

/**
 * 保存扣子配置
 */
export const saveKouziConfig = async (req: Request, res: Response) => {
  try {
    const repository = AppDataSource.getRepository(ConfigKouzi);
    const { enabled, appId, authMode, privateKey, publicKey, personalAccessToken } = req.body;

    // 查询第一条记录（按 id 升序）
    let config = await repository.findOne({
      where: {},
      order: { id: 'ASC' }
    });

    if (config) {
      config.enabled = enabled;
      config.appId = appId || '';
      config.authMode = authMode || 'jwt';
      config.privateKey = privateKey || '';
      config.publicKey = publicKey || '';
      config.personalAccessToken = personalAccessToken || '';
    } else {
      config = repository.create({
        enabled: enabled || false,
        appId: appId || '',
        authMode: authMode || 'jwt',
        privateKey: privateKey || '',
        publicKey: publicKey || '',
        personalAccessToken: personalAccessToken || ''
      });
    }

    await repository.save(config);

    return res.json({
      success: true,
      message: '配置保存成功',
      data: config
    });
  } catch (error: any) {
    console.error('保存扣子配置失败:', error);
    return res.status(500).json({
      success: false,
      message: '保存配置失败',
      error: error.message
    });
  }
};

/**
 * 获取验证配置
 */
export const getVerificationConfig = async (req: Request, res: Response) => {
  try {
    const repository = AppDataSource.getRepository(ConfigVerification);
    let config = await repository.findOne({ where: { id: 1 } });

    if (!config) {
      config = repository.create({
        smsProvider: 'aliyun',
        smsEnabled: false,
        emailProvider: 'smtp',
        emailEnabled: false,
        storageProvider: 'aliyun',
        storageEnabled: false
      });
      await repository.save(config);
    }

    return res.json({
      success: true,
      data: config
    });
  } catch (error: any) {
    console.error('获取验证配置失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取配置失败',
      error: error.message
    });
  }
};

/**
 * 保存验证配置
 */
export const saveVerificationConfig = async (req: Request, res: Response) => {
  try {
    const repository = AppDataSource.getRepository(ConfigVerification);
    const configData = req.body;

    let config = await repository.findOne({ where: { id: 1 } });

    if (config) {
      // 更新现有配置
      Object.assign(config, configData);
    } else {
      // 创建新配置
      config = repository.create();
      Object.assign(config, configData);
    }

    await repository.save(config);

    return res.json({
      success: true,
      message: '配置保存成功',
      data: config
    });
  } catch (error: any) {
    console.error('保存验证配置失败:', error);
    return res.status(500).json({
      success: false,
      message: '保存配置失败',
      error: error.message
    });
  }
};

/**
 * 获取免费额度配置
 */
export const getFreeQuotaConfig = async (req: Request, res: Response) => {
  try {
    const repository = AppDataSource.getRepository(ConfigFreeQuota);
    let config = await repository.findOne({ where: { id: 1 } });

    if (!config) {
      config = repository.create({
        enabled: false,
        newUserQuota: 100,
        quotaValidDays: 30,
        maxConsumePerSession: 20,
        insufficientQuotaMessage: '您今日的免费使用额度已用完，请购买套餐获取更多额度'
      });
      await repository.save(config);
    }

    return res.json({
      success: true,
      data: config
    });
  } catch (error: any) {
    console.error('获取免费额度配置失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取配置失败',
      error: error.message
    });
  }
};

/**
 * 保存免费额度配置
 */
export const saveFreeQuotaConfig = async (req: Request, res: Response) => {
  try {
    const repository = AppDataSource.getRepository(ConfigFreeQuota);
    const { enabled, newUserQuota, quotaValidDays, maxConsumePerSession, insufficientQuotaMessage } = req.body;

    let config = await repository.findOne({ where: { id: 1 } });

    if (config) {
      config.enabled = enabled;
      config.newUserQuota = newUserQuota || 100;
      config.quotaValidDays = quotaValidDays || 30;
      config.maxConsumePerSession = maxConsumePerSession || 20;
      config.insufficientQuotaMessage = insufficientQuotaMessage || '您今日的免费使用额度已用完，请购买套餐获取更多额度';
    } else {
      config = repository.create({
        enabled: enabled || false,
        newUserQuota: newUserQuota || 100,
        quotaValidDays: quotaValidDays || 30,
        maxConsumePerSession: maxConsumePerSession || 20,
        insufficientQuotaMessage: insufficientQuotaMessage || '您今日的免费使用额度已用完，请购买套餐获取更多额度'
      });
    }

    await repository.save(config);

    return res.json({
      success: true,
      message: '配置保存成功',
      data: config
    });
  } catch (error: any) {
    console.error('保存免费额度配置失败:', error);
    return res.status(500).json({
      success: false,
      message: '保存配置失败',
      error: error.message
    });
  }
};

/**
 * 获取推荐配置
 */
export const getReferralConfig = async (req: Request, res: Response) => {
  try {
    // 使用原生SQL查询以确保获取所有字段，包括新添加的shareImageUrl
    const queryResult = await AppDataSource.query('SELECT * FROM config_referral WHERE id = 1');

    let config;
    if (queryResult && queryResult.length > 0) {
      const rawData = queryResult[0];
      // 将数据库字段名转换为camelCase
      config = {
        id: rawData.id,
        enabled: rawData.enabled,
        posterBackgroundImage: rawData.poster_background_image,
        posterQrCodeSize: rawData.poster_qr_code_size,
        posterQrCodePositionX: rawData.poster_qr_code_position_x,
        posterQrCodePositionY: rawData.poster_qr_code_position_y,
        shareTitle: rawData.share_title,
        shareDescription: rawData.share_description,
        shareImageUrl: rawData.share_image_url || '', // 新添加的字段
        inviteRewardPoints: rawData.invite_reward_points,
        registerRewardPoints: rawData.register_reward_points,
        inviterReward: rawData.inviter_reward,
        maxInviteCount: rawData.max_invite_count,
        rewardValidDays: rawData.reward_valid_days,
        inviteSuccessMessage: rawData.invite_success_message,
        inviteeReward: rawData.invitee_reward,
        promoterCashback: rawData.promoter_cashback,
        promoterCommission: rawData.promoter_commission,
        minWithdrawal: rawData.min_withdrawal,
        withdrawalChannel: rawData.withdrawal_channel,
        withdrawalFee: rawData.withdrawal_fee,
        createdAt: rawData.created_at,
        updatedAt: rawData.updated_at
      };
    } else {
      // 如果没有数据，创建默认配置
      const repository = AppDataSource.getRepository(ConfigReferral);
      const newConfig = repository.create({
        enabled: false,
        posterBackgroundImage: '',
        posterQrCodeSize: 202,
        posterQrCodePositionX: 200,
        posterQrCodePositionY: 400,
        shareTitle: 'AI智能体使用邀请',
        shareDescription: '注册即可获得额外免费使用次数，快来体验智能AI助手！',
        shareImageUrl: '',
        inviteRewardPoints: 100,
        registerRewardPoints: 50,
        inviterReward: 50,
        maxInviteCount: 10,
        rewardValidDays: 60,
        inviteSuccessMessage: '恭喜您成功邀请好友，获得{reward}次免费使用奖励！',
        inviteeReward: 30,
        promoterCashback: 5.00,
        promoterCommission: 2.00,
        minWithdrawal: 1.00,
        withdrawalChannel: 'alipay',
        withdrawalFee: 0.00
      });
      await repository.save(newConfig);
      config = newConfig;
    }

    // 临时修复：手动添加shareImageUrl字段
    const configWithShareImage = {
      ...config,
      shareImageUrl: config.shareImageUrl || ''
    };

    return res.json({
      success: true,
      data: configWithShareImage
    });
  } catch (error: any) {
    console.error('获取推荐配置失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取配置失败',
      error: error.message
    });
  }
};

/**
 * 保存推荐配置
 */
export const saveReferralConfig = async (req: Request, res: Response) => {
  try {
    const repository = AppDataSource.getRepository(ConfigReferral);
    const configData = req.body;

    let config = await repository.findOne({ where: { id: 1 } });

    if (config) {
      // 更新现有配置
      Object.assign(config, configData);
    } else {
      // 创建新配置
      config = repository.create();
      Object.assign(config, configData);
    }

    await repository.save(config);

    return res.json({
      success: true,
      message: '配置保存成功',
      data: config
    });
  } catch (error: any) {
    console.error('保存推荐配置失败:', error);
    return res.status(500).json({
      success: false,
      message: '保存配置失败',
      error: error.message
    });
  }
};

/**
 * 获取系统配置
 */
export const getSystemConfig = async (req: Request, res: Response) => {
  try {
    const repository = AppDataSource.getRepository(ConfigSystem);
    let config = await repository.findOne({ where: { id: 1 } });

    if (!config) {
      config = repository.create({
        siteName: 'AI Agent Admin',
        siteLogo: '',
        siteDescription: '专业的AI智能体管理平台',
        copyright: '© 2023 AI Agent Admin',
        contactEmail: '<EMAIL>',
        beianInfo: '',
        siteUrl: '',
        maxLoginAttempts: 5,
        loginLockTime: 30,
        sessionTimeout: 120,
        maintenanceMode: false,
        primaryColor: '#1890ff'
      });
      await repository.save(config);
    }

    return res.json({
      success: true,
      data: config
    });
  } catch (error: any) {
    console.error('获取系统配置失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取配置失败',
      error: error.message
    });
  }
};

/**
 * 保存系统配置
 */
export const saveSystemConfig = async (req: Request, res: Response) => {
  try {
    const repository = AppDataSource.getRepository(ConfigSystem);
    const configData = req.body;

    let config = await repository.findOne({ where: { id: 1 } });

    if (config) {
      // 更新现有配置
      Object.assign(config, configData);
    } else {
      // 创建新配置
      config = repository.create();
      Object.assign(config, configData);
    }

    await repository.save(config);

    return res.json({
      success: true,
      message: '配置保存成功',
      data: config
    });
  } catch (error: any) {
    console.error('保存系统配置失败:', error);
    return res.status(500).json({
      success: false,
      message: '保存配置失败',
      error: error.message
    });
  }
};

/**
 * 获取所有配置状态
 */
export const getAllConfigStatus = async (req: Request, res: Response) => {
  try {
    const configStatus: any = {};

    // 获取各个配置的启用状态
    const wechatOfficialRepo = AppDataSource.getRepository(ConfigWechatOfficial);
    const wechatOfficial = await wechatOfficialRepo.findOne({ where: { id: 1 } });
    configStatus.wechatOfficial = wechatOfficial?.enabled || false;

    const wechatMiniprogramRepo = AppDataSource.getRepository(ConfigWechatMiniprogram);
    const wechatMiniprogram = await wechatMiniprogramRepo.findOne({ where: { id: 1 } });
    configStatus.wechatMiniprogram = wechatMiniprogram?.enabled || false;

    const wechatPayRepo = AppDataSource.getRepository(ConfigWechatPay);
    const wechatPay = await wechatPayRepo.findOne({ where: { id: 1 } });
    configStatus.wechatPay = wechatPay?.enabled || false;

    const alipayRepo = AppDataSource.getRepository(ConfigAlipay);
    const alipay = await alipayRepo.findOne({
      where: {},
      order: { id: 'DESC' }
    });
    configStatus.alipay = alipay?.enabled || false;

    const kouziRepo = AppDataSource.getRepository(ConfigKouzi);
    const kouzi = await kouziRepo.findOne({ where: { id: 1 } });
    configStatus.kouzi = kouzi?.enabled || false;

    const verificationRepo = AppDataSource.getRepository(ConfigVerification);
    const verification = await verificationRepo.findOne({ where: { id: 1 } });
    configStatus.verification = verification?.smsEnabled || verification?.emailEnabled || false;

    const freeQuotaRepo = AppDataSource.getRepository(ConfigFreeQuota);
    const freeQuota = await freeQuotaRepo.findOne({ where: { id: 1 } });
    configStatus.freeQuota = freeQuota?.enabled || false;

    const referralRepo = AppDataSource.getRepository(ConfigReferral);
    const referral = await referralRepo.findOne({ where: { id: 1 } });
    configStatus.referral = referral?.enabled || false;

    return res.json({
      success: true,
      data: configStatus
    });
  } catch (error: any) {
    console.error('获取配置状态失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取配置状态失败',
      error: error.message
    });
  }
};
