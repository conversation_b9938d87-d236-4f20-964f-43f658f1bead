import { AppDataSource } from '../data-source';

async function createConfigTable() {
  console.log('🔧 开始创建config表和初始化免费额度配置...\n');

  try {
    // 初始化数据库连接
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
      console.log('✅ 数据库连接已建立\n');
    }

    // 创建config表
    console.log('1. 创建config表...');
    await AppDataSource.query(`
      CREATE TABLE IF NOT EXISTS \`config\` (
        \`id\` int NOT NULL AUTO_INCREMENT,
        \`type\` varchar(50) NOT NULL COMMENT '配置类型',
        \`key\` varchar(100) NOT NULL COMMENT '配置键',
        \`value\` text NOT NULL COMMENT '配置值',
        \`description\` varchar(255) DEFAULT NULL COMMENT '描述',
        \`createdAt\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        \`updatedAt\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (\`id\`),
        UNIQUE KEY \`uk_type_key\` (\`type\`, \`key\`)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='系统配置表'
    `);
    console.log('✅ config表创建成功');

    // 插入免费额度配置
    console.log('\n2. 插入免费额度配置...');
    await AppDataSource.query(`
      INSERT INTO \`config\` (\`type\`, \`key\`, \`value\`, \`description\`) VALUES
      ('free-quota', 'enabled', 'true', '是否启用免费额度'),
      ('free-quota', 'new_user_quota', '100', '新用户免费额度'),
      ('free-quota', 'quota_valid_days', '30', '免费额度有效期（天）'),
      ('free-quota', 'max_consume_per_session', '20', '每日最大消耗点数'),
      ('free-quota', 'insufficient_quota_message', '您今日的免费使用额度已用完，请购买套餐获取更多额度', '额度不足提示信息')
      ON DUPLICATE KEY UPDATE 
      \`value\` = VALUES(\`value\`),
      \`description\` = VALUES(\`description\`)
    `);
    console.log('✅ 免费额度配置插入成功');

    // 查询插入结果
    console.log('\n3. 查询配置结果...');
    const configs = await AppDataSource.query(`
      SELECT \`type\`, \`key\`, \`value\`, \`description\`
      FROM \`config\`
      WHERE \`type\` = 'free-quota'
      ORDER BY \`key\`
    `);

    console.log('📋 免费额度配置:');
    configs.forEach((config: any) => {
      console.log(`  ${config.key}: ${config.value} (${config.description})`);
    });

    console.log('\n✅ 配置表创建和初始化完成！');

  } catch (error) {
    console.error('❌ 创建失败:', error);
  } finally {
    // 关闭数据库连接
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('\n🔌 数据库连接已关闭');
    }
  }
}

// 运行创建
createConfigTable().catch(console.error);
