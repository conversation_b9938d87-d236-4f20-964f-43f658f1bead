/**
 * 支付宝配置实体类
 */
import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('config_alipay')
export class ConfigAlipay {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'tinyint', width: 1, default: 0, comment: '是否启用' })
  enabled: boolean;

  @Column({ name: 'sandbox_mode', type: 'tinyint', width: 1, default: 1, comment: '是否为沙箱模式' })
  sandboxMode: boolean;

  @Column({ name: 'app_id', length: 50, default: '', comment: '支付宝应用ID' })
  appId: string;

  @Column({ name: 'private_key', type: 'text', nullable: true, comment: '应用私钥' })
  privateKey: string;

  @Column({ name: 'alipay_public_key', type: 'text', nullable: true, comment: '支付宝公钥' })
  alipayPublicKey: string;

  @Column({ name: 'sign_type', length: 10, default: 'RSA2', comment: '签名类型' })
  signType: string;

  @Column({ name: 'notify_url', length: 255, default: '', comment: '支付结果通知URL' })
  notifyUrl: string;

  @Column({ name: 'enable_pc_pay', type: 'tinyint', width: 1, default: 1, comment: '是否启用电脑网站支付' })
  enablePcPay: boolean;

  @Column({ name: 'pc_return_url', length: 255, default: '', comment: '电脑网站支付同步返回地址' })
  pcReturnUrl: string;

  @Column({ name: 'enable_wap_pay', type: 'tinyint', width: 1, default: 1, comment: '是否启用手机网站支付' })
  enableWapPay: boolean;

  @Column({ name: 'wap_return_url', length: 255, default: '', comment: '手机网站支付同步返回地址' })
  wapReturnUrl: string;

  @Column({ name: 'wap_quit_url', length: 255, default: '', comment: '手机网站支付中途退出返回地址' })
  wapQuitUrl: string;

  @CreateDateColumn({ name: 'created_at', comment: '创建时间' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', comment: '更新时间' })
  updatedAt: Date;
}
