import { AppDataSource } from '../data-source';

async function testSimpleQuota() {
  console.log('🧪 测试简化额度显示...\n');

  try {
    // 初始化数据库连接
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
      console.log('✅ 数据库连接已建立\n');
    }

    // 导入DailyLimitService
    const { DailyLimitService } = await import('../services/daily-limit.service');

    // 获取测试用户
    const users = await AppDataSource.query(`
      SELECT id, name, points, packageId, packageExpiredAt
      FROM members 
      WHERE id IN (59, 60, 61)
      ORDER BY id ASC
    `);

    for (const user of users) {
      console.log(`\n=== 用户 ${user.id} (${user.name}) ===`);
      console.log(`总点数余额: ${user.points} 点数`);

      try {
        // 获取分类额度详情
        const quotaDetails = await DailyLimitService.getQuotaDetailsByCategory(user.id);
        
        console.log('\n📋 简化额度显示:');
        
        // 1. 免费额度
        console.log(`🎁 免费额度 - 日限额: ${quotaDetails.freeQuota.dailyLimit} 点数`);

        // 2. 限制套餐额度
        if (quotaDetails.limitedPackages.length > 0) {
          quotaDetails.limitedPackages.forEach(pkg => {
            console.log(`💎 ${pkg.title} - 日限额: ${pkg.dailyLimit} 点数`);
          });
        }

        // 3. 无限制套餐额度
        if (quotaDetails.unlimitedPackages.length > 0) {
          quotaDetails.unlimitedPackages.forEach(pkg => {
            console.log(`🚀 ${pkg.title} - 无限制额度`);
          });
        }

        // 4. 无套餐情况
        if (quotaDetails.limitedPackages.length === 0 && quotaDetails.unlimitedPackages.length === 0) {
          console.log('📦 套餐额度 - 暂无有效套餐');
        }

      } catch (error) {
        console.error(`❌ 获取用户 ${user.id} 额度详情失败:`, error);
      }
    }

    console.log('\n✅ 简化额度显示测试完成！');
    
    console.log('\n📋 显示内容总结:');
    console.log('1. 总点数余额 - 用户当前可用点数');
    console.log('2. 免费额度 - 日限额多少点数');
    console.log('3. 限制套餐额度 - 套餐名称 + 日限额多少点数');
    console.log('4. 无限制套餐额度 - 套餐名称 + 无限制额度');
    console.log('5. 无套餐提示 - 暂无有效套餐');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    // 关闭数据库连接
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('\n🔌 数据库连接已关闭');
    }
  }
}

// 运行测试
testSimpleQuota().catch(console.error);
