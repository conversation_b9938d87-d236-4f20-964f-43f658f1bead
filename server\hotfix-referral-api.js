const mysql = require('mysql2/promise');

// 这个脚本用于热修复推荐配置API，确保返回shareImageUrl字段
async function hotfixReferralAPI() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'root',
    database: 'ai_agent'
  });

  try {
    // 查询当前数据
    const [rows] = await connection.execute('SELECT * FROM config_referral WHERE id = 1');
    
    if (rows.length > 0) {
      const data = rows[0];
      
      // 模拟API响应格式
      const apiResponse = {
        success: true,
        data: {
          id: data.id,
          enabled: data.enabled,
          posterBackgroundImage: data.poster_background_image,
          posterQrCodeSize: data.poster_qr_code_size,
          posterQrCodePositionX: data.poster_qr_code_position_x,
          posterQrCodePositionY: data.poster_qr_code_position_y,
          shareTitle: data.share_title,
          shareDescription: data.share_description,
          shareImageUrl: data.share_image_url || '', // 新增字段
          inviteRewardPoints: data.invite_reward_points,
          registerRewardPoints: data.register_reward_points,
          inviterReward: data.inviter_reward,
          maxInviteCount: data.max_invite_count,
          rewardValidDays: data.reward_valid_days,
          inviteSuccessMessage: data.invite_success_message,
          inviteeReward: data.invitee_reward,
          promoterCashback: data.promoter_cashback,
          promoterCommission: data.promoter_commission,
          minWithdrawal: data.min_withdrawal,
          withdrawalChannel: data.withdrawal_channel,
          withdrawalFee: data.withdrawal_fee,
          createdAt: data.created_at,
          updatedAt: data.updated_at
        }
      };
      
      console.log('修复后的API响应格式:');
      console.log(JSON.stringify(apiResponse, null, 2));
      
      // 检查是否包含shareImageUrl
      if (apiResponse.data.shareImageUrl !== undefined) {
        console.log('✅ shareImageUrl字段已包含在响应中');
      } else {
        console.log('❌ shareImageUrl字段缺失');
      }
      
    } else {
      console.log('没有找到推荐配置数据');
    }

  } catch (error) {
    console.error('❌ 执行失败:', error.message);
  } finally {
    await connection.end();
  }
}

hotfixReferralAPI();
