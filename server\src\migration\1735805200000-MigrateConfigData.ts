import { MigrationInterface, QueryRunner } from "typeorm";

export class MigrateConfigData1735805200000 implements MigrationInterface {
    name = 'MigrateConfigData1735805200000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        console.log('开始迁移配置数据...');

        // 1. 迁移微信公众号配置
        await this.migrateWechatOfficialConfig(queryRunner);

        // 2. 迁移微信小程序配置
        await this.migrateWechatMiniprogramConfig(queryRunner);

        // 3. 迁移微信支付配置
        await this.migrateWechatPayConfig(queryRunner);

        // 4. 迁移支付宝配置
        await this.migrateAlipayConfig(queryRunner);

        // 5. 迁移扣子配置
        await this.migrateKouziConfig(queryRunner);

        // 6. 迁移验证配置
        await this.migrateVerificationConfig(queryRunner);

        // 7. 迁移免费额度配置
        await this.migrateFreeQuotaConfig(queryRunner);

        // 8. 迁移推荐奖励配置
        await this.migrateReferralConfig(queryRunner);

        // 9. 迁移系统配置
        await this.migrateSystemConfig(queryRunner);

        console.log('配置数据迁移完成！');
    }

    private async migrateWechatOfficialConfig(queryRunner: QueryRunner): Promise<void> {
        console.log('迁移微信公众号配置...');
        
        const configs = await queryRunner.query(`
            SELECT \`key\`, \`value\` FROM \`system_config\` 
            WHERE \`type\` = 'wechat_official'
        `);

        const configMap: any = {};
        configs.forEach((config: any) => {
            configMap[config.key] = config.value || '';
        });

        await queryRunner.query(`
            INSERT INTO \`config_wechat_official\` 
            (\`enabled\`, \`app_id\`, \`app_secret\`, \`token\`, \`encoding_aes_key\`) 
            VALUES (?, ?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE 
            \`enabled\` = VALUES(\`enabled\`),
            \`app_id\` = VALUES(\`app_id\`),
            \`app_secret\` = VALUES(\`app_secret\`),
            \`token\` = VALUES(\`token\`),
            \`encoding_aes_key\` = VALUES(\`encoding_aes_key\`)
        `, [
            configMap.enabled === 'true' ? 1 : 0,
            configMap.app_id || '',
            configMap.app_secret || '',
            configMap.token || '',
            configMap.encoding_aes_key || ''
        ]);
    }

    private async migrateWechatMiniprogramConfig(queryRunner: QueryRunner): Promise<void> {
        console.log('迁移微信小程序配置...');
        
        const configs = await queryRunner.query(`
            SELECT \`key\`, \`value\` FROM \`system_config\` 
            WHERE \`type\` = 'wechat_miniprogram'
        `);

        const configMap: any = {};
        configs.forEach((config: any) => {
            configMap[config.key] = config.value || '';
        });

        await queryRunner.query(`
            INSERT INTO \`config_wechat_miniprogram\` 
            (\`enabled\`, \`app_id\`, \`app_secret\`) 
            VALUES (?, ?, ?)
            ON DUPLICATE KEY UPDATE 
            \`enabled\` = VALUES(\`enabled\`),
            \`app_id\` = VALUES(\`app_id\`),
            \`app_secret\` = VALUES(\`app_secret\`)
        `, [
            configMap.enabled === 'true' ? 1 : 0,
            configMap.app_id || '',
            configMap.app_secret || ''
        ]);
    }

    private async migrateWechatPayConfig(queryRunner: QueryRunner): Promise<void> {
        console.log('迁移微信支付配置...');
        
        const configs = await queryRunner.query(`
            SELECT \`key\`, \`value\` FROM \`system_config\` 
            WHERE \`type\` = 'payment_wechat'
        `);

        const configMap: any = {};
        configs.forEach((config: any) => {
            configMap[config.key] = config.value || '';
        });

        await queryRunner.query(`
            INSERT INTO \`config_wechat_pay\` 
            (\`enabled\`, \`sandbox\`, \`mch_id\`, \`api_v3_key\`, \`api_key\`, \`serial_no\`, \`certificate_content\`, \`private_key_content\`, \`notify_url\`) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE 
            \`enabled\` = VALUES(\`enabled\`),
            \`sandbox\` = VALUES(\`sandbox\`),
            \`mch_id\` = VALUES(\`mch_id\`),
            \`api_v3_key\` = VALUES(\`api_v3_key\`),
            \`api_key\` = VALUES(\`api_key\`),
            \`serial_no\` = VALUES(\`serial_no\`),
            \`certificate_content\` = VALUES(\`certificate_content\`),
            \`private_key_content\` = VALUES(\`private_key_content\`),
            \`notify_url\` = VALUES(\`notify_url\`)
        `, [
            configMap.enabled === 'true' ? 1 : 0,
            configMap.sandbox === 'true' ? 1 : 0,
            configMap.mch_id || '',
            configMap.secret_key || '', // API v3密钥
            configMap.secret_key || '', // API v2密钥（兼容）
            '', // 证书序列号
            '', // 证书内容
            '', // 私钥内容
            configMap.notify_url || ''
        ]);
    }

    private async migrateAlipayConfig(queryRunner: QueryRunner): Promise<void> {
        console.log('迁移支付宝配置...');
        
        const configs = await queryRunner.query(`
            SELECT \`key\`, \`value\` FROM \`system_config\` 
            WHERE \`type\` = 'payment_alipay'
        `);

        const configMap: any = {};
        configs.forEach((config: any) => {
            configMap[config.key] = config.value || '';
        });

        await queryRunner.query(`
            INSERT INTO \`config_alipay\` 
            (\`enabled\`, \`sandbox_mode\`, \`app_id\`, \`private_key\`, \`alipay_public_key\`, \`sign_type\`, \`notify_url\`, \`enable_pc_pay\`, \`pc_return_url\`, \`enable_wap_pay\`, \`wap_return_url\`, \`wap_quit_url\`) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE 
            \`enabled\` = VALUES(\`enabled\`),
            \`sandbox_mode\` = VALUES(\`sandbox_mode\`),
            \`app_id\` = VALUES(\`app_id\`),
            \`private_key\` = VALUES(\`private_key\`),
            \`alipay_public_key\` = VALUES(\`alipay_public_key\`),
            \`sign_type\` = VALUES(\`sign_type\`),
            \`notify_url\` = VALUES(\`notify_url\`),
            \`enable_pc_pay\` = VALUES(\`enable_pc_pay\`),
            \`pc_return_url\` = VALUES(\`pc_return_url\`),
            \`enable_wap_pay\` = VALUES(\`enable_wap_pay\`),
            \`wap_return_url\` = VALUES(\`wap_return_url\`),
            \`wap_quit_url\` = VALUES(\`wap_quit_url\`)
        `, [
            configMap.enabled === 'true' ? 1 : 0,
            configMap.sandbox === 'true' ? 1 : 0,
            configMap.app_id || '',
            configMap.private_key || '',
            configMap.alipay_public_key || '',
            'RSA2',
            configMap.notify_url || '',
            1, // 默认启用电脑网站支付
            '', // 电脑网站支付同步返回地址
            1, // 默认启用手机网站支付
            '', // 手机网站支付同步返回地址
            ''  // 手机网站支付中途退出返回地址
        ]);
    }

    private async migrateKouziConfig(queryRunner: QueryRunner): Promise<void> {
        console.log('迁移扣子配置...');
        
        const configs = await queryRunner.query(`
            SELECT \`key\`, \`value\` FROM \`system_config\` 
            WHERE \`type\` = 'kouzi'
        `);

        const configMap: any = {};
        configs.forEach((config: any) => {
            configMap[config.key] = config.value || '';
        });

        await queryRunner.query(`
            INSERT INTO \`config_kouzi\` 
            (\`enabled\`, \`app_id\`, \`auth_mode\`, \`private_key\`, \`public_key\`, \`personal_access_token\`) 
            VALUES (?, ?, ?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE 
            \`enabled\` = VALUES(\`enabled\`),
            \`app_id\` = VALUES(\`app_id\`),
            \`auth_mode\` = VALUES(\`auth_mode\`),
            \`private_key\` = VALUES(\`private_key\`),
            \`public_key\` = VALUES(\`public_key\`),
            \`personal_access_token\` = VALUES(\`personal_access_token\`)
        `, [
            configMap.enabled === 'true' ? 1 : 0,
            configMap.app_id || '',
            configMap.auth_mode || 'jwt',
            configMap.private_key || '',
            configMap.public_key || '',
            configMap.personal_access_token || ''
        ]);
    }

    private async migrateVerificationConfig(queryRunner: QueryRunner): Promise<void> {
        console.log('迁移验证配置...');

        const configs = await queryRunner.query(`
            SELECT \`key\`, \`value\` FROM \`system_config\`
            WHERE \`type\` = 'verification'
        `);

        const configMap: any = {};
        configs.forEach((config: any) => {
            configMap[config.key] = config.value || '';
        });

        await queryRunner.query(`
            INSERT INTO \`config_verification\`
            (\`sms_provider\`, \`sms_enabled\`, \`sms_aliyun_access_key_id\`, \`sms_aliyun_access_key_secret\`, \`sms_aliyun_sign_name\`, \`sms_aliyun_template_code\`,
             \`sms_tencent_secret_id\`, \`sms_tencent_secret_key\`, \`sms_tencent_app_id\`, \`sms_tencent_sign_name\`, \`sms_tencent_template_id\`,
             \`sms_huawei_app_key\`, \`sms_huawei_app_secret\`, \`sms_huawei_sign_name\`, \`sms_huawei_template_id\`,
             \`email_provider\`, \`email_enabled\`, \`email_smtp_host\`, \`email_smtp_port\`, \`email_smtp_from_email\`, \`email_smtp_password\`, \`email_smtp_from_name\`, \`email_smtp_ssl\`,
             \`email_aliyun_access_key_id\`, \`email_aliyun_access_key_secret\`, \`email_aliyun_from_email\`, \`email_aliyun_from_name\`,
             \`email_tencent_secret_id\`, \`email_tencent_secret_key\`, \`email_tencent_from_email\`, \`email_tencent_from_name\`,
             \`storage_provider\`, \`storage_enabled\`, \`storage_aliyun_access_key_id\`, \`storage_aliyun_access_key_secret\`, \`storage_aliyun_bucket\`, \`storage_aliyun_endpoint\`, \`storage_aliyun_base_url\`,
             \`storage_tencent_secret_id\`, \`storage_tencent_secret_key\`, \`storage_tencent_bucket\`, \`storage_tencent_base_url\`)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE \`id\` = \`id\`
        `, [
            configMap.sms_provider || 'aliyun',
            configMap.sms_enabled === 'true' ? 1 : 0,
            configMap.sms_aliyun_access_key_id || '',
            configMap.sms_aliyun_access_key_secret || '',
            configMap.sms_aliyun_sign_name || '',
            configMap.sms_aliyun_template_code || '',
            configMap.sms_tencent_secret_id || '',
            configMap.sms_tencent_secret_key || '',
            configMap.sms_tencent_app_id || '',
            configMap.sms_tencent_sign_name || '',
            configMap.sms_tencent_template_id || '',
            configMap.sms_huawei_app_key || '',
            configMap.sms_huawei_app_secret || '',
            configMap.sms_huawei_sign_name || '',
            configMap.sms_huawei_template_id || '',
            configMap.email_provider || 'smtp',
            configMap.email_enabled === 'true' ? 1 : 0,
            configMap.email_smtp_host || '',
            parseInt(configMap.email_smtp_port) || 587,
            configMap.email_smtp_from_email || '',
            configMap.email_smtp_password || '',
            configMap.email_smtp_from_name || '',
            configMap.email_smtp_ssl === 'true' ? 1 : 0,
            configMap.email_aliyun_access_key_id || '',
            configMap.email_aliyun_access_key_secret || '',
            configMap.email_aliyun_from_email || '',
            configMap.email_aliyun_from_name || '',
            configMap.email_tencent_secret_id || '',
            configMap.email_tencent_secret_key || '',
            configMap.email_tencent_from_email || '',
            configMap.email_tencent_from_name || '',
            configMap.storage_provider || 'aliyun',
            configMap.storage_enabled === 'true' ? 1 : 0,
            configMap.storage_aliyun_access_key_id || '',
            configMap.storage_aliyun_access_key_secret || '',
            configMap.storage_aliyun_bucket || '',
            configMap.storage_aliyun_endpoint || '',
            configMap.storage_aliyun_base_url || '',
            configMap.storage_tencent_secret_id || '',
            configMap.storage_tencent_secret_key || '',
            configMap.storage_tencent_bucket || '',
            configMap.storage_tencent_base_url || ''
        ]);
    }

    private async migrateFreeQuotaConfig(queryRunner: QueryRunner): Promise<void> {
        console.log('迁移免费额度配置...');

        const configs = await queryRunner.query(`
            SELECT \`key\`, \`value\` FROM \`system_config\`
            WHERE \`type\` = 'free-quota'
        `);

        const configMap: any = {};
        configs.forEach((config: any) => {
            configMap[config.key] = config.value || '';
        });

        await queryRunner.query(`
            INSERT INTO \`config_free_quota\`
            (\`enabled\`, \`new_user_quota\`, \`quota_valid_days\`, \`max_consume_per_session\`, \`insufficient_quota_message\`)
            VALUES (?, ?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE
            \`enabled\` = VALUES(\`enabled\`),
            \`new_user_quota\` = VALUES(\`new_user_quota\`),
            \`quota_valid_days\` = VALUES(\`quota_valid_days\`),
            \`max_consume_per_session\` = VALUES(\`max_consume_per_session\`),
            \`insufficient_quota_message\` = VALUES(\`insufficient_quota_message\`)
        `, [
            configMap.enabled === 'true' ? 1 : 0,
            parseInt(configMap.new_user_quota) || 100,
            parseInt(configMap.quota_valid_days) || 30,
            parseInt(configMap.max_consume_per_session) || 20,
            configMap.insufficient_quota_message || '您今日的免费使用额度已用完，请购买套餐获取更多额度'
        ]);
    }

    private async migrateReferralConfig(queryRunner: QueryRunner): Promise<void> {
        console.log('迁移推荐奖励配置...');

        const configs = await queryRunner.query(`
            SELECT \`key\`, \`value\` FROM \`system_config\`
            WHERE \`type\` = 'referral'
        `);

        const configMap: any = {};
        configs.forEach((config: any) => {
            configMap[config.key] = config.value || '';
        });

        await queryRunner.query(`
            INSERT INTO \`config_referral\`
            (\`enabled\`, \`poster_background_image\`, \`poster_qr_code_size\`, \`poster_qr_code_position_x\`, \`poster_qr_code_position_y\`,
             \`share_title\`, \`share_description\`, \`invite_reward_points\`, \`register_reward_points\`, \`inviter_reward\`, \`max_invite_count\`, \`reward_valid_days\`, \`invite_success_message\`,
             \`invitee_reward\`, \`promoter_cashback\`, \`promoter_commission\`, \`min_withdrawal\`, \`withdrawal_channel\`, \`withdrawal_fee\`)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE \`id\` = \`id\`
        `, [
            0, // 默认不启用
            configMap.poster_background_image || '',
            parseInt(configMap.poster_qr_code_size) || 202,
            parseInt(configMap.poster_qr_code_position_x) || 200,
            parseInt(configMap.poster_qr_code_position_y) || 400,
            configMap.share_title || 'AI智能体使用邀请',
            configMap.share_description || '注册即可获得额外免费使用次数，快来体验智能AI助手！',
            parseInt(configMap.invite_reward_points) || 100,
            parseInt(configMap.register_reward_points) || 50,
            50, // 邀请人奖励
            10, // 最大邀请次数
            60, // 奖励有效期
            '恭喜您成功邀请好友，获得{reward}次免费使用奖励！',
            30, // 被邀请人奖励
            5.00, // 推广人返现比例
            2.00, // 推广人佣金比例
            1.00, // 最低提现金额
            'alipay', // 提现渠道
            0.00  // 提现手续费
        ]);
    }

    private async migrateSystemConfig(queryRunner: QueryRunner): Promise<void> {
        console.log('迁移系统配置...');

        const configs = await queryRunner.query(`
            SELECT \`key\`, \`value\` FROM \`system_config\`
            WHERE \`type\` = 'system'
        `);

        const configMap: any = {};
        configs.forEach((config: any) => {
            configMap[config.key] = config.value || '';
        });

        await queryRunner.query(`
            INSERT INTO \`config_system\`
            (\`site_name\`, \`site_logo\`, \`site_description\`, \`copyright\`, \`contact_email\`, \`beian_info\`, \`site_url\`,
             \`max_login_attempts\`, \`login_lock_time\`, \`session_timeout\`, \`maintenance_mode\`, \`primary_color\`)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE
            \`site_name\` = VALUES(\`site_name\`),
            \`site_logo\` = VALUES(\`site_logo\`),
            \`site_description\` = VALUES(\`site_description\`),
            \`copyright\` = VALUES(\`copyright\`),
            \`contact_email\` = VALUES(\`contact_email\`),
            \`beian_info\` = VALUES(\`beian_info\`),
            \`site_url\` = VALUES(\`site_url\`),
            \`max_login_attempts\` = VALUES(\`max_login_attempts\`),
            \`login_lock_time\` = VALUES(\`login_lock_time\`),
            \`session_timeout\` = VALUES(\`session_timeout\`),
            \`maintenance_mode\` = VALUES(\`maintenance_mode\`),
            \`primary_color\` = VALUES(\`primary_color\`)
        `, [
            configMap.site_name || 'AI Agent Admin',
            configMap.site_logo || '',
            '专业的AI智能体管理平台',
            configMap.copyright || '© 2023 AI Agent Admin',
            configMap.contact_email || '<EMAIL>',
            '', // 备案信息
            '', // 站点URL
            parseInt(configMap.max_login_attempts) || 5,
            parseInt(configMap.login_lock_time) || 30,
            parseInt(configMap.session_timeout) || 120,
            configMap.maintenance_mode === 'true' ? 1 : 0,
            '#1890ff' // 主题色
        ]);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        console.log('回滚配置数据迁移...');
        // 清空所有新配置表的数据
        await queryRunner.query(`DELETE FROM \`config_system\``);
        await queryRunner.query(`DELETE FROM \`config_referral\``);
        await queryRunner.query(`DELETE FROM \`config_free_quota\``);
        await queryRunner.query(`DELETE FROM \`config_verification\``);
        await queryRunner.query(`DELETE FROM \`config_kouzi\``);
        await queryRunner.query(`DELETE FROM \`config_alipay\``);
        await queryRunner.query(`DELETE FROM \`config_wechat_pay\``);
        await queryRunner.query(`DELETE FROM \`config_wechat_miniprogram\``);
        await queryRunner.query(`DELETE FROM \`config_wechat_official\``);
    }
}
