import { AppDataSource } from '../data-source';

async function addMiniprogramFields() {
  try {
    await AppDataSource.initialize();
    console.log('数据库连接成功');

    const queryRunner = AppDataSource.createQueryRunner();
    
    try {
      // 需要添加的字段列表
      const fieldsToAdd = [
        {
          name: 'verify_file_name',
          sql: 'varchar(255) NOT NULL DEFAULT \'\' COMMENT \'域名验证文件名\''
        },
        {
          name: 'verify_file_content',
          sql: 'text COMMENT \'域名验证文件内容\''
        },
        {
          name: 'banner_ad_enabled',
          sql: 'tinyint(1) NOT NULL DEFAULT 0 COMMENT \'是否启用激励视频广告\''
        },
        {
          name: 'banner_ad_id',
          sql: 'varchar(100) NOT NULL DEFAULT \'\' COMMENT \'激励视频广告ID\''
        },
        {
          name: 'interstitial_ad_enabled',
          sql: 'tinyint(1) NOT NULL DEFAULT 0 COMMENT \'是否启用插屏广告\''
        },
        {
          name: 'interstitial_ad_id',
          sql: 'varchar(100) NOT NULL DEFAULT \'\' COMMENT \'插屏广告ID\''
        },
        {
          name: 'interstitial_ad_count',
          sql: 'int NOT NULL DEFAULT 0 COMMENT \'插屏广告次数\''
        },
        {
          name: 'interstitial_ad_interval',
          sql: 'int NOT NULL DEFAULT 0 COMMENT \'插屏广告重播间隔\''
        },
        {
          name: 'video_ad_enabled',
          sql: 'tinyint(1) NOT NULL DEFAULT 0 COMMENT \'是否启用视频广告\''
        },
        {
          name: 'video_ad_id',
          sql: 'varchar(100) NOT NULL DEFAULT \'\' COMMENT \'视频广告ID\''
        }
      ];

      for (const field of fieldsToAdd) {
        // 检查字段是否已存在
        const checkField = await queryRunner.query(`
          SELECT COLUMN_NAME 
          FROM INFORMATION_SCHEMA.COLUMNS 
          WHERE TABLE_SCHEMA = DATABASE() 
          AND TABLE_NAME = 'config_wechat_miniprogram' 
          AND COLUMN_NAME = '${field.name}'
        `);
        
        if (checkField.length === 0) {
          console.log(`添加 ${field.name} 字段...`);
          await queryRunner.query(`
            ALTER TABLE \`config_wechat_miniprogram\` 
            ADD COLUMN \`${field.name}\` ${field.sql}
          `);
          console.log(`✅ ${field.name} 字段添加成功`);
        } else {
          console.log(`${field.name} 字段已存在`);
        }
      }

    } finally {
      await queryRunner.release();
    }

    console.log('✅ 所有字段检查完成');
    
  } catch (error) {
    console.error('❌ 添加字段失败:', error);
  } finally {
    await AppDataSource.destroy();
  }
}

addMiniprogramFields();
