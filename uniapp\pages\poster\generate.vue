<template>
	<view class="poster-page">
		<!-- 导航栏 -->
		<view class="nav-bar">
			<view class="nav-left" @click="goBack">
				<text class="nav-icon">←</text>
			</view>
			<view class="nav-title">生成推广海报</view>
			<view class="nav-right"></view>
		</view>

		<!-- 加载状态 -->
		<view v-if="generating" class="loading-container">
			<view class="loading-spinner"></view>
			<text class="loading-text">正在生成推广海报...</text>
		</view>

		<!-- 海报内容 -->
		<view v-else class="poster-content">
			<!-- 海报预览 -->
			<view class="poster-preview">
				<canvas 
					canvas-id="posterCanvas" 
					:style="{ width: canvasWidth + 'px', height: canvasHeight + 'px' }"
					class="poster-canvas"
				></canvas>
			</view>

			<!-- 邀请信息 -->
			<view class="invite-info">
				<view class="info-card">
					<view class="info-row">
						<text class="info-label">邀请码:</text>
						<text class="info-value">{{ inviteCode }}</text>
						<view class="copy-btn" @click="copyInviteCode">
							<text class="copy-text">复制</text>
						</view>
					</view>
					<view class="info-row">
						<text class="info-label">邀请链接:</text>
						<text class="info-value">{{ inviteUrl }}</text>
						<view class="copy-btn" @click="copyInviteUrl">
							<text class="copy-text">复制</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 操作按钮 -->
			<view class="action-buttons">
				<button class="action-btn primary-btn" @click="savePoster" :disabled="saving">
					{{ saving ? '保存中...' : '保存海报' }}
				</button>
				<button class="action-btn secondary-btn" @click="sharePoster" :disabled="sharing">
					{{ sharing ? '分享中...' : '分享海报' }}
				</button>
				<button class="action-btn tertiary-btn" @click="regeneratePoster" :disabled="generating">
					重新生成
				</button>
			</view>
		</view>
	</view>
</template>

<script>
import { userStore } from '@/api/members.js'
import { API_BASE_URL } from '@/config/index.js'

export default {
	name: 'PosterGenerate',
	data() {
		return {
			generating: false,
			saving: false,
			sharing: false,
			inviteCode: '',
			inviteUrl: '',
			userInfo: {},
			canvasWidth: 300,
			canvasHeight: 400,
			posterConfig: {
				backgroundImage: '',
				qrCodeSize: 100,
				qrCodePosition: { x: 100, y: 250 }
			},
			shareConfig: {
				title: 'AI智能体使用邀请',
				description: '注册即可获得额外免费使用次数，快来体验智能AI助手！'
			}
		}
	},

	onLoad() {
		this.initPoster()
	},

	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack()
		},

		// 初始化海报
		async initPoster() {
			try {
				this.generating = true

				// 获取用户信息
				await this.getUserInfo()

				// 获取后台海报配置
				await this.loadPosterConfig()

				// 生成邀请码
				await this.generateInviteCode()

				// 生成海报
				await this.generatePoster()

			} catch (error) {
				console.error('初始化海报失败:', error)
				uni.showToast({
					title: '生成海报失败',
					icon: 'none'
				})
			} finally {
				this.generating = false
			}
		},

		// 获取用户信息
		async getUserInfo() {
			try {
				const currentUser = userStore.getCurrentUser()
				if (currentUser) {
					this.userInfo = currentUser
				}
			} catch (error) {
				console.error('获取用户信息失败:', error)
			}
		},

		// 加载后台海报配置
		async loadPosterConfig() {
			try {
				const response = await uni.request({
					url: `${API_BASE_URL}/api/config/referral`,
					method: 'GET'
				})

				if (response.statusCode === 200 && response.data.success) {
					const config = response.data.data

					// 更新海报配置 - 使用旧格式字段名
					if (config.poster_background_image) {
						this.posterConfig.backgroundImage = config.poster_background_image
					}
					if (config.poster_qr_code_size) {
						this.posterConfig.qrCodeSize = parseInt(config.poster_qr_code_size)
					}
					if (config.poster_qr_code_position_x !== undefined && config.poster_qr_code_position_y !== undefined) {
						this.posterConfig.qrCodePosition.x = parseInt(config.poster_qr_code_position_x)
						this.posterConfig.qrCodePosition.y = parseInt(config.poster_qr_code_position_y)
					}

					// 更新分享配置
					if (config.share_title) {
						this.shareConfig.title = config.share_title
					}
					if (config.share_description) {
						this.shareConfig.description = config.share_description
					}
				}
			} catch (error) {
				console.error('加载海报配置失败:', error)
				// 使用默认配置
			}
		},

		// 生成邀请码
		async generateInviteCode() {
			try {
				const token = userStore.getToken()
				if (!token) {
					throw new Error('用户未登录')
				}

				const response = await uni.request({
					url: `${API_BASE_URL}/api/config/generate-invite-code`,
					method: 'POST',
					header: {
						'Authorization': `Bearer ${token}`,
						'Content-Type': 'application/json'
					}
				})

				if (response.statusCode === 200 && response.data.success) {
					this.inviteCode = response.data.data.inviteCode
					this.inviteUrl = response.data.data.inviteUrl
				} else {
					throw new Error(response.data?.message || '生成邀请码失败')
				}
			} catch (error) {
				console.error('生成邀请码失败:', error)
				// 降级到本地生成
				const userId = this.userInfo.id || 'user123'
				this.inviteCode = `INV${userId}${Date.now().toString().slice(-6)}`
				this.inviteUrl = `${API_BASE_URL}?inviteCode=${this.inviteCode}&inviter=${userId}`
			}
		},

		// 生成海报
		async generatePoster() {
			try {
				const ctx = uni.createCanvasContext('posterCanvas', this)
				
				// 清空画布
				ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight)
				
				// 绘制背景
				await this.drawBackground(ctx)
				
				// 绘制内容
				await this.drawContent(ctx)
				
				// 绘制二维码
				await this.drawQRCode(ctx)
				
				// 绘制到画布
				ctx.draw()
				
			} catch (error) {
				console.error('生成海报失败:', error)
				throw error
			}
		},

		// 绘制背景
		async drawBackground(ctx) {
			if (this.posterConfig.backgroundImage) {
				try {
					// 下载背景图片到本地临时文件
					const downloadResult = await uni.downloadFile({
						url: this.posterConfig.backgroundImage
					})

					if (downloadResult.statusCode === 200) {
						// 绘制背景图片
						ctx.drawImage(downloadResult.tempFilePath, 0, 0, this.canvasWidth, this.canvasHeight)
						return
					}
				} catch (error) {
					console.error('加载背景图片失败:', error)
				}
			}

			// 使用默认渐变背景
			const gradient = ctx.createLinearGradient(0, 0, 0, this.canvasHeight)
			gradient.addColorStop(0, '#667eea')
			gradient.addColorStop(1, '#764ba2')

			ctx.setFillStyle(gradient)
			ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight)
		},

		// 绘制内容
		async drawContent(ctx) {
			// 绘制标题
			ctx.setFillStyle('#ffffff')
			ctx.setFontSize(24)
			ctx.setTextAlign('center')
			ctx.fillText(this.shareConfig.title, this.canvasWidth / 2, 60)

			// 绘制描述
			ctx.setFontSize(16)
			ctx.setFillStyle('#f0f0f0')
			
			const description = this.shareConfig.description
			const maxWidth = this.canvasWidth - 40
			const lineHeight = 25
			
			// 简单的文字换行处理
			const lines = this.wrapText(description, maxWidth, ctx)
			let y = 100
			
			lines.forEach(line => {
				ctx.fillText(line, this.canvasWidth / 2, y)
				y += lineHeight
			})

			// 绘制用户信息
			if (this.userInfo.nickname || this.userInfo.username) {
				ctx.setFontSize(14)
				ctx.setFillStyle('#ffffff')
				ctx.fillText(
					`邀请人: ${this.userInfo.nickname || this.userInfo.username}`,
					this.canvasWidth / 2,
					this.canvasHeight - 60
				)
			}

			// 绘制邀请码
			ctx.setFontSize(12)
			ctx.setFillStyle('#ffeb3b')
			ctx.fillText(`邀请码: ${this.inviteCode}`, this.canvasWidth / 2, this.canvasHeight - 30)
		},

		// 绘制二维码
		async drawQRCode(ctx) {
			const qrX = this.posterConfig.qrCodePosition.x
			const qrY = this.posterConfig.qrCodePosition.y
			const qrSize = this.posterConfig.qrCodeSize

			try {
				// 生成二维码数据
				const qrcode = require('qrcode-generator')
				const qr = qrcode(4, 'L')
				qr.addData(this.inviteUrl)
				qr.make()

				// 获取二维码模块数据
				const moduleCount = qr.getModuleCount()
				const cellSize = qrSize / moduleCount

				// 绘制白色背景
				ctx.setFillStyle('#ffffff')
				ctx.fillRect(qrX - 5, qrY - 5, qrSize + 10, qrSize + 10)

				// 绘制二维码
				ctx.setFillStyle('#000000')
				for (let row = 0; row < moduleCount; row++) {
					for (let col = 0; col < moduleCount; col++) {
						if (qr.isDark(row, col)) {
							const x = qrX + col * cellSize
							const y = qrY + row * cellSize
							ctx.fillRect(x, y, cellSize, cellSize)
						}
					}
				}
			} catch (error) {
				console.error('生成二维码失败:', error)

				// 绘制占位符
				ctx.setFillStyle('#ffffff')
				ctx.fillRect(qrX - 5, qrY - 5, qrSize + 10, qrSize + 10)

				ctx.setFillStyle('#666666')
				ctx.fillRect(qrX, qrY, qrSize, qrSize)

				ctx.setFillStyle('#ffffff')
				ctx.setFontSize(12)
				ctx.setTextAlign('center')
				ctx.fillText('二维码', qrX + qrSize / 2, qrY + qrSize / 2)
			}
		},

		// 文字换行处理
		wrapText(text, maxWidth, ctx) {
			const words = text.split('')
			const lines = []
			let currentLine = ''

			for (let i = 0; i < words.length; i++) {
				const testLine = currentLine + words[i]
				const metrics = ctx.measureText(testLine)
				
				if (metrics.width > maxWidth && currentLine !== '') {
					lines.push(currentLine)
					currentLine = words[i]
				} else {
					currentLine = testLine
				}
			}
			
			if (currentLine) {
				lines.push(currentLine)
			}
			
			return lines
		},

		// 保存海报
		async savePoster() {
			try {
				this.saving = true
				
				// 保存画布为图片
				const result = await new Promise((resolve, reject) => {
					uni.canvasToTempFilePath({
						canvasId: 'posterCanvas',
						success: resolve,
						fail: reject
					}, this)
				})

				// 保存到相册
				await uni.saveImageToPhotosAlbum({
					filePath: result.tempFilePath
				})

				uni.showToast({
					title: '海报已保存到相册',
					icon: 'success'
				})
				
			} catch (error) {
				console.error('保存海报失败:', error)
				uni.showToast({
					title: '保存失败',
					icon: 'none'
				})
			} finally {
				this.saving = false
			}
		},

		// 分享海报
		async sharePoster() {
			try {
				this.sharing = true
				
				// 生成临时图片
				const result = await new Promise((resolve, reject) => {
					uni.canvasToTempFilePath({
						canvasId: 'posterCanvas',
						success: resolve,
						fail: reject
					}, this)
				})

				// 分享图片
				await uni.share({
					type: 'image',
					imageUrl: result.tempFilePath,
					title: this.shareConfig.title,
					summary: this.shareConfig.description
				})
				
			} catch (error) {
				console.error('分享海报失败:', error)
				// 降级到复制邀请链接
				this.copyInviteUrl()
			} finally {
				this.sharing = false
			}
		},

		// 复制邀请码
		copyInviteCode() {
			uni.setClipboardData({
				data: this.inviteCode,
				success: () => {
					uni.showToast({
						title: '邀请码已复制',
						icon: 'success'
					})
				}
			})
		},

		// 复制邀请链接
		copyInviteUrl() {
			uni.setClipboardData({
				data: this.inviteUrl,
				success: () => {
					uni.showToast({
						title: '邀请链接已复制',
						icon: 'success'
					})
				}
			})
		},

		// 重新生成海报
		async regeneratePoster() {
			await this.initPoster()
			uni.showToast({
				title: '海报已重新生成',
				icon: 'success'
			})
		}
	}
}
</script>

<style scoped>
.poster-page {
	min-height: 100vh;
	background: #f5f5f5;
}

.nav-bar {
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 44px;
	padding: 0 15px;
	background: #fff;
	border-bottom: 1px solid #eee;
}

.nav-left {
	width: 44px;
	height: 44px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.nav-icon {
	font-size: 20px;
	color: #333;
}

.nav-title {
	font-size: 18px;
	font-weight: 600;
	color: #333;
}

.nav-right {
	width: 44px;
}

.loading-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 400px;
}

.loading-spinner {
	width: 40px;
	height: 40px;
	border: 3px solid #f3f3f3;
	border-top: 3px solid #667eea;
	border-radius: 50%;
	animation: spin 1s linear infinite;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.loading-text {
	margin-top: 16px;
	font-size: 16px;
	color: #666;
}

.poster-content {
	padding: 20px;
}

.poster-preview {
	display: flex;
	justify-content: center;
	margin-bottom: 20px;
	padding: 20px;
	background: #fff;
	border-radius: 12px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.poster-canvas {
	border-radius: 8px;
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.invite-info {
	margin-bottom: 20px;
}

.info-card {
	background: #fff;
	border-radius: 12px;
	padding: 16px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.info-row {
	display: flex;
	align-items: center;
	margin-bottom: 12px;
}

.info-row:last-child {
	margin-bottom: 0;
}

.info-label {
	font-size: 14px;
	color: #666;
	min-width: 80px;
}

.info-value {
	flex: 1;
	font-size: 14px;
	color: #333;
	margin-right: 8px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.copy-btn {
	padding: 4px 12px;
	background: #667eea;
	border-radius: 4px;
}

.copy-text {
	font-size: 12px;
	color: #fff;
}

.action-buttons {
	display: flex;
	flex-direction: column;
	gap: 12px;
}

.action-btn {
	height: 48px;
	border-radius: 8px;
	font-size: 16px;
	font-weight: 600;
	border: none;
}

.primary-btn {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: #fff;
}

.secondary-btn {
	background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
	color: #fff;
}

.tertiary-btn {
	background: #f5f5f5;
	color: #666;
	border: 1px solid #ddd;
}

.action-btn:disabled {
	opacity: 0.6;
}
</style>
