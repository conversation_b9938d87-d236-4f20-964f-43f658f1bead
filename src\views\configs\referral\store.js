import { reactive, ref } from 'vue'
import axios from 'axios'

// 单例模式共享Store
const invitationData = ref([])
const partnerData = ref([])
const shareData = ref([])
const isLoading = ref(false)
const type = 'referral'

// 加载所有配置数据
const loadAllConfig = async () => {
  isLoading.value = true

  try {
    const response = await axios.get(`/api/config/frontend/${type}`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token') || ''}`,
        'Content-Type': 'application/json'
      }
    })

    if (response.data && response.data.code === 200 && response.data.data) {
      // 新配置系统返回的数据格式: {code: 200, data: {configValue: {...}}}
      const configValue = response.data.data.configValue || {}

      // 处理字段名映射 - 将数据库字段名转换为前端期望的字段名
      const mappedData = {
        enabled: configValue.enabled,
        shareTitle: configValue.shareTitle,
        shareDescription: configValue.shareDescription,
        shareImageUrl: configValue.shareImageUrl || '', // 新增的分享图片字段
        posterBackgroundImage: configValue.posterBackgroundImage,
        posterQrCodeSize: configValue.posterQrCodeSize,
        posterQrCodePositionX: configValue.posterQrCodePositionX,
        posterQrCodePositionY: configValue.posterQrCodePositionY,
        inviteRewardPoints: configValue.inviteRewardPoints,
        registerRewardPoints: configValue.registerRewardPoints,
        inviterReward: configValue.inviterReward,
        maxInviteCount: configValue.maxInviteCount,
        rewardValidDays: configValue.rewardValidDays,
        inviteSuccessMessage: configValue.inviteSuccessMessage,
        inviteeReward: configValue.inviteeReward,
        promoterCashback: configValue.promoterCashback,
        promoterCommission: configValue.promoterCommission,
        minWithdrawal: configValue.minWithdrawal,
        withdrawalChannel: configValue.withdrawalChannel,
        withdrawalFee: configValue.withdrawalFee
      }

      // 将mappedData转换为旧格式的数组，以保持兼容性
      const allData = Object.keys(mappedData).map(key => ({
        key: key,
        value: mappedData[key]
      }))

      // 将数据分类到三个不同的数组
      // 邀请配置相关字段
      const invitationKeys = ['inviterReward', 'maxInviteCount', 'rewardValidDays', 'enabled']
      // 合伙人配置相关字段
      const partnerKeys = ['inviteeReward', 'promoterCashback', 'promoterCommission', 'minWithdrawal', 'withdrawalChannel', 'withdrawalFee']
      // 分享配置相关字段
      const shareKeys = [
        'shareTitle',
        'shareDescription',
        'shareImageUrl',
        'posterBackgroundImage',
        'posterQrCodeSize',
        'posterQrCodePositionX',
        'posterQrCodePositionY'
      ]

      invitationData.value = allData.filter(item => invitationKeys.includes(item.key))
      partnerData.value = allData.filter(item => partnerKeys.includes(item.key))
      shareData.value = allData.filter(item => shareKeys.includes(item.key))

      return true
    }
    return false
  } catch (error) {
    console.error('加载推荐奖励配置失败:', error)
    return false
  } finally {
    isLoading.value = false
  }
}

// 更新邀请配置数据
const updateInvitationData = (data) => {
  // 如果传入的是对象格式，转换为数组格式
  if (data && typeof data === 'object' && !Array.isArray(data)) {
    invitationData.value = Object.keys(data).map(key => ({
      key: key,
      value: data[key]
    }))
  } else {
    invitationData.value = data || []
  }
}

// 更新合伙人配置数据
const updatePartnerData = (data) => {
  // 如果传入的是对象格式，转换为数组格式
  if (data && typeof data === 'object' && !Array.isArray(data)) {
    partnerData.value = Object.keys(data).map(key => ({
      key: key,
      value: data[key]
    }))
  } else {
    partnerData.value = data || []
  }
}

// 更新分享配置数据
const updateShareData = (data) => {
  // 如果传入的是对象格式，转换为数组格式
  if (data && typeof data === 'object' && !Array.isArray(data)) {
    shareData.value = Object.keys(data).map(key => ({
      key: key,
      value: data[key]
    }))
  } else {
    shareData.value = data || []
  }
}

// 创建一个单例引用
export const useReferralStore = () => {
  return {
    invitationData,
    partnerData,
    shareData,
    isLoading,
    loadAllConfig,
    updateInvitationData,
    updatePartnerData,
    updateShareData
  }
} 