import { MigrationInterface, QueryRunner } from "typeorm";

export class AddLogoFieldsToConfigSystem1735910000000 implements MigrationInterface {
    name = 'AddLogoFieldsToConfigSystem1735910000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        console.log('开始添加Logo字段到config_system表...');

        // 添加Logo相关字段
        await queryRunner.query(`
            ALTER TABLE \`config_system\` 
            ADD COLUMN \`main_logo\` VARCHAR(500) NOT NULL DEFAULT '' COMMENT '主Logo图片URL' AFTER \`site_logo\`,
            ADD COLUMN \`sidebar_logo\` VARCHAR(500) NOT NULL DEFAULT '' COMMENT '侧边栏Logo图片URL' AFTER \`main_logo\`,
            ADD COLUMN \`mini_logo\` VARCHAR(500) NOT NULL DEFAULT '' COMMENT '迷你Logo图片URL' AFTER \`sidebar_logo\`,
            ADD COLUMN \`user_logo\` VARCHAR(500) NOT NULL DEFAULT '' COMMENT '用户Logo图片URL' AFTER \`mini_logo\`,
            ADD COLUMN \`favicon\` VARCHAR(500) NOT NULL DEFAULT '' COMMENT '网站图标URL' AFTER \`user_logo\`,
            ADD COLUMN \`customer_service_image\` VARCHAR(500) NOT NULL DEFAULT '' COMMENT '客服图片URL' AFTER \`favicon\`
        `);

        console.log('Logo字段添加完成！');
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        console.log('开始回滚Logo字段...');

        // 删除Logo相关字段
        await queryRunner.query(`
            ALTER TABLE \`config_system\` 
            DROP COLUMN \`customer_service_image\`,
            DROP COLUMN \`favicon\`,
            DROP COLUMN \`user_logo\`,
            DROP COLUMN \`mini_logo\`,
            DROP COLUMN \`sidebar_logo\`,
            DROP COLUMN \`main_logo\`
        `);

        console.log('Logo字段回滚完成！');
    }
}
