import { AppDataSource } from '../data-source';
import { purchasePackage } from '../controllers/package.controller';

async function testPurchaseAPI() {
  console.log('🧪 开始测试套餐购买API限制...\n');

  try {
    // 初始化数据库连接
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
      console.log('✅ 数据库连接已建立\n');
    }

    // 模拟请求和响应对象
    const createMockReqRes = (userId: number, packageId: number) => {
      const req = {
        user: { id: userId },
        body: { packageId, paymentMethod: 'balance' }
      } as any;

      const res = {
        status: (code: number) => ({
          json: (data: any) => {
            console.log(`HTTP ${code}:`, data);
            return data;
          }
        })
      } as any;

      return { req, res };
    };

    console.log('=== 测试场景 1: VIP用户尝试购买限制套餐 ===');
    console.log('用户59 (娃哈哈1) 已有有效的限制套餐，尝试购买新的限制套餐...');
    
    const { req: req1, res: res1 } = createMockReqRes(59, 18); // 用户59购买套餐18
    await purchasePackage(req1, res1);

    console.log('\n=== 测试场景 2: 免费用户购买限制套餐 ===');
    console.log('用户60 (测试用户) 无套餐，尝试购买限制套餐...');
    
    // 先给用户60增加余额
    await AppDataSource.query(`
      UPDATE members SET balance = 200 WHERE id = 60
    `);
    console.log('✅ 已为用户60增加余额到200元');
    
    const { req: req2, res: res2 } = createMockReqRes(60, 18); // 用户60购买套餐18
    await purchasePackage(req2, res2);

    console.log('\n=== 测试场景 3: VIP用户购买无限制套餐 ===');
    console.log('用户59 (娃哈哈1) 已有限制套餐，尝试购买无限制套餐...');
    
    // 先给用户59增加余额
    await AppDataSource.query(`
      UPDATE members SET balance = 1000 WHERE id = 59
    `);
    console.log('✅ 已为用户59增加余额到1000元');
    
    const { req: req3, res: res3 } = createMockReqRes(59, 17); // 用户59购买套餐17 (无限制)
    await purchasePackage(req3, res3);

    console.log('\n✅ API测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    // 关闭数据库连接
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('\n🔌 数据库连接已关闭');
    }
  }
}

// 运行测试
testPurchaseAPI().catch(console.error);
