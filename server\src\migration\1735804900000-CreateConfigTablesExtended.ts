import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateConfigTablesExtended1735804900000 implements MigrationInterface {
    name = 'CreateConfigTablesExtended1735804900000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // 6. 创建验证配置表（短信、邮箱、对象存储）
        await queryRunner.query(`
            CREATE TABLE IF NOT EXISTS \`config_verification\` (
                \`id\` int(11) NOT NULL AUTO_INCREMENT,
                -- 短信验证配置
                \`sms_provider\` varchar(20) NOT NULL DEFAULT 'aliyun' COMMENT '短信服务提供商',
                \`sms_enabled\` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否启用短信验证',
                -- 阿里云短信配置
                \`sms_aliyun_access_key_id\` varchar(100) NOT NULL DEFAULT '' COMMENT '阿里云短信Access Key ID',
                \`sms_aliyun_access_key_secret\` varchar(255) NOT NULL DEFAULT '' COMMENT '阿里云短信Access Key Secret',
                \`sms_aliyun_sign_name\` varchar(50) NOT NULL DEFAULT '' COMMENT '阿里云短信签名',
                \`sms_aliyun_template_code\` varchar(50) NOT NULL DEFAULT '' COMMENT '阿里云短信验证码模板代码',
                -- 腾讯云短信配置
                \`sms_tencent_secret_id\` varchar(100) NOT NULL DEFAULT '' COMMENT '腾讯云短信Secret ID',
                \`sms_tencent_secret_key\` varchar(255) NOT NULL DEFAULT '' COMMENT '腾讯云短信Secret Key',
                \`sms_tencent_app_id\` varchar(50) NOT NULL DEFAULT '' COMMENT '腾讯云短信应用ID',
                \`sms_tencent_sign_name\` varchar(50) NOT NULL DEFAULT '' COMMENT '腾讯云短信签名',
                \`sms_tencent_template_id\` varchar(50) NOT NULL DEFAULT '' COMMENT '腾讯云短信验证码模板ID',
                -- 华为云短信配置
                \`sms_huawei_app_key\` varchar(100) NOT NULL DEFAULT '' COMMENT '华为云短信App Key',
                \`sms_huawei_app_secret\` varchar(255) NOT NULL DEFAULT '' COMMENT '华为云短信App Secret',
                \`sms_huawei_sign_name\` varchar(50) NOT NULL DEFAULT '' COMMENT '华为云短信签名',
                \`sms_huawei_template_id\` varchar(50) NOT NULL DEFAULT '' COMMENT '华为云短信验证码模板ID',
                -- 邮箱验证配置
                \`email_provider\` varchar(20) NOT NULL DEFAULT 'smtp' COMMENT '邮件服务提供商',
                \`email_enabled\` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否启用邮箱验证',
                -- SMTP邮箱配置
                \`email_smtp_host\` varchar(100) NOT NULL DEFAULT '' COMMENT 'SMTP服务器地址',
                \`email_smtp_port\` int(11) NOT NULL DEFAULT 587 COMMENT 'SMTP服务器端口',
                \`email_smtp_from_email\` varchar(100) NOT NULL DEFAULT '' COMMENT 'SMTP发件人邮箱',
                \`email_smtp_password\` varchar(255) NOT NULL DEFAULT '' COMMENT 'SMTP邮箱密码或授权码',
                \`email_smtp_from_name\` varchar(100) NOT NULL DEFAULT '' COMMENT 'SMTP发件人名称',
                \`email_smtp_ssl\` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'SMTP是否启用SSL',
                -- 阿里云邮件推送配置
                \`email_aliyun_access_key_id\` varchar(100) NOT NULL DEFAULT '' COMMENT '阿里云邮件推送Access Key ID',
                \`email_aliyun_access_key_secret\` varchar(255) NOT NULL DEFAULT '' COMMENT '阿里云邮件推送Access Key Secret',
                \`email_aliyun_from_email\` varchar(100) NOT NULL DEFAULT '' COMMENT '阿里云邮件推送发件人邮箱',
                \`email_aliyun_from_name\` varchar(100) NOT NULL DEFAULT '' COMMENT '阿里云邮件推送发件人名称',
                -- 腾讯云邮件推送配置
                \`email_tencent_secret_id\` varchar(100) NOT NULL DEFAULT '' COMMENT '腾讯云邮件推送Secret ID',
                \`email_tencent_secret_key\` varchar(255) NOT NULL DEFAULT '' COMMENT '腾讯云邮件推送Secret Key',
                \`email_tencent_from_email\` varchar(100) NOT NULL DEFAULT '' COMMENT '腾讯云邮件推送发件人邮箱',
                \`email_tencent_from_name\` varchar(100) NOT NULL DEFAULT '' COMMENT '腾讯云邮件推送发件人名称',
                -- 对象存储配置
                \`storage_provider\` varchar(20) NOT NULL DEFAULT 'aliyun' COMMENT '对象存储服务提供商',
                \`storage_enabled\` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否启用对象存储',
                -- 阿里云OSS配置
                \`storage_aliyun_access_key_id\` varchar(100) NOT NULL DEFAULT '' COMMENT '阿里云OSS Access Key ID',
                \`storage_aliyun_access_key_secret\` varchar(255) NOT NULL DEFAULT '' COMMENT '阿里云OSS Access Key Secret',
                \`storage_aliyun_bucket\` varchar(100) NOT NULL DEFAULT '' COMMENT '阿里云OSS Bucket名称',
                \`storage_aliyun_endpoint\` varchar(255) NOT NULL DEFAULT '' COMMENT '阿里云OSS访问域名',
                \`storage_aliyun_base_url\` varchar(255) NOT NULL DEFAULT '' COMMENT '阿里云OSS基础URL',
                -- 腾讯云COS配置
                \`storage_tencent_secret_id\` varchar(100) NOT NULL DEFAULT '' COMMENT '腾讯云COS Secret ID',
                \`storage_tencent_secret_key\` varchar(255) NOT NULL DEFAULT '' COMMENT '腾讯云COS Secret Key',
                \`storage_tencent_bucket\` varchar(100) NOT NULL DEFAULT '' COMMENT '腾讯云COS Bucket名称',
                \`storage_tencent_base_url\` varchar(255) NOT NULL DEFAULT '' COMMENT '腾讯云COS基础URL',
                \`created_at\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                \`updated_at\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                PRIMARY KEY (\`id\`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='验证配置表'
        `);

        // 7. 创建免费额度配置表
        await queryRunner.query(`
            CREATE TABLE IF NOT EXISTS \`config_free_quota\` (
                \`id\` int(11) NOT NULL AUTO_INCREMENT,
                \`enabled\` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用免费额度',
                \`new_user_quota\` int(11) NOT NULL DEFAULT 100 COMMENT '新用户免费额度',
                \`quota_valid_days\` int(11) NOT NULL DEFAULT 30 COMMENT '免费额度有效期（天）',
                \`max_consume_per_session\` int(11) NOT NULL DEFAULT 20 COMMENT '每日最大消耗点数',
                \`insufficient_quota_message\` varchar(500) NOT NULL DEFAULT '您今日的免费使用额度已用完，请购买套餐获取更多额度' COMMENT '额度不足提示信息',
                \`created_at\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                \`updated_at\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                PRIMARY KEY (\`id\`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='免费额度配置表'
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE IF EXISTS \`config_free_quota\``);
        await queryRunner.query(`DROP TABLE IF EXISTS \`config_verification\``);
    }
}
