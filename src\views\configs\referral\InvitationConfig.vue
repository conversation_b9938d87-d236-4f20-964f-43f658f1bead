<template>
  <div class="invitation-config">
    <n-form :model="formData" label-placement="left" label-width="80px" size="small">
      <n-form-item label="邀请赠送" required>
        <n-input-number v-model:value="formData.inviterReward" :min="0" :step="10" size="small" placeholder="50" />
      </n-form-item>

      <n-form-item label="邀请上限" required>
        <n-input-number v-model:value="formData.maxInviteCount" :min="0" :step="1" size="small" placeholder="10" />
      </n-form-item>

      <n-form-item label="有效期" required>
        <n-input-number v-model:value="formData.rewardValidDays" :min="1" :step="1" size="small" placeholder="60" />
      </n-form-item>

      <n-form-item label="启用奖励">
        <n-switch v-model:value="formData.enabled" size="small" />
      </n-form-item>
    </n-form>

    <div class="action-buttons">
      <n-button type="primary" size="small" @click="handleSave" :loading="saving">保存配置</n-button>
      <n-button size="small" @click="handleReset" :loading="resetting">重置配置</n-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import {
  NForm,
  NFormItem,
  NInputNumber,
  NSwitch,
  NButton,
  useMessage
} from 'naive-ui'
import axios from 'axios'
import { useReferralStore } from './store'

const message = useMessage()
const saving = ref(false)
const resetting = ref(false)
const referralStore = useReferralStore()

// 定义组件类型属性
const type = 'referral'

const formData = reactive({
  inviterReward: 50,
  maxInviteCount: 10,
  rewardValidDays: 60,
  enabled: true
})

const handleSave = async () => {
  if (!formData.inviterReward && formData.inviterReward !== 0) {
    message.error('邀请人奖励额度不能为空')
    return
  }
  
  saving.value = true
  
  try {
    // 准备符合API格式的数据 - 新的对象格式
    const apiData = {
      inviterReward: formData.inviterReward,
      maxInviteCount: formData.maxInviteCount,
      rewardValidDays: formData.rewardValidDays,
      enabled: formData.enabled
    }
    
    // 获取其他页面的配置数据
    const partnerData = referralStore.partnerData.value
    const shareData = referralStore.shareData.value

    // 合并所有配置数据 - 使用对象格式
    const fullApiData = {
      ...apiData,
      ...partnerData,
      ...shareData
    }

    // 尝试保存到API
    const response = await axios.post(`/api/config/frontend/${type}`, fullApiData, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
      }
    })
    
    message.success('配置保存成功')
    // 更新store中的邀请配置数据
    referralStore.updateInvitationData(apiData)
    // 触发事件
    window.dispatchEvent(new CustomEvent('referral-config-saved'))
  } catch (error) {
    console.error('保存配置失败:', error)
    message.error('保存配置失败: ' + (error.message || '未知错误'))
  } finally {
    saving.value = false
  }
}

const handleReset = async () => {
  if (!window.confirm('确定要重置邀请配置吗？这将清空所有已填写的邀请配置值。')) {
    return
  }
  
  resetting.value = true
  try {
    Object.assign(formData, {
      inviterReward: 50,
      maxInviteCount: 10,
      rewardValidDays: 60,
      enabled: true
    })
    message.success('邀请配置已重置')
  } catch (error) {
    console.error('重置配置失败:', error)
    message.error('重置配置失败: ' + (error.message || '未知错误'))
  } finally {
    resetting.value = false
  }
}

const loadConfig = async () => {
  try {
    // 从store中获取数据
    const storeData = referralStore.invitationData.value
    if (storeData && storeData.length > 0) {
      // 转换格式
      const configData = {}
      storeData.forEach(item => {
        configData[item.key] = item.value
      })
      
      Object.assign(formData, {
        inviterReward: parseInt(configData.inviterReward || '50'),
        maxInviteCount: parseInt(configData.maxInviteCount || '10'),
        rewardValidDays: parseInt(configData.rewardValidDays || '60'),
        enabled: configData.enabled === true || configData.enabled === 'true'
      })
    } else {
      // 如果store中没有数据，从API加载
      await referralStore.loadAllConfig()
      
      // 等待加载完成后更新表单
      const configData = {}
      referralStore.invitationData.value.forEach(item => {
        configData[item.key] = item.value
      })
      
      Object.assign(formData, {
        inviterReward: parseInt(configData.inviterReward || '50'),
        maxInviteCount: parseInt(configData.maxInviteCount || '10'),
        rewardValidDays: parseInt(configData.rewardValidDays || '60'),
        enabled: configData.enabled === true || configData.enabled === 'true'
      })
    }
  } catch (error) {
    console.warn('加载邀请配置失败:', error)
    message.error('加载配置失败: ' + (error.message || '未知错误'))
  }
}

onMounted(() => {
  loadConfig()
})

// 对外暴露方法
defineExpose({
  loadConfig
})
</script>

<style scoped>
.invitation-config {
  padding: 12px;
}

.action-buttons {
  margin-top: 16px;
  display: flex;
  gap: 8px;
}
</style>