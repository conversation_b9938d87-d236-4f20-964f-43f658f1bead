<template>
  <div class="partner-config">
    <n-form :model="formData" label-placement="left" label-width="80px" size="small">
      <n-form-item label="升级条件" required>
        <n-input-number v-model:value="formData.inviteeReward" :min="0" :step="10" size="small" placeholder="10" />
      </n-form-item>

      <n-form-item label="一级比例" required>
        <n-input-group>
          <n-input-number v-model:value="formData.promoterCashback" :min="0" :step="1" size="small" placeholder="10" />
          <n-button-group>
            <n-button size="small">%</n-button>
          </n-button-group>
        </n-input-group>
      </n-form-item>

      <n-form-item label="二级比例" required>
        <n-input-group>
          <n-input-number v-model:value="formData.promoterCommission" :min="0" :step="1" size="small" placeholder="5" />
          <n-button-group>
            <n-button size="small">%</n-button>
          </n-button-group>
        </n-input-group>
      </n-form-item>

      <n-form-item label="最低提现" required>
        <n-input-group>
          <n-input-number v-model:value="formData.minWithdrawal" :min="1" :step="1" size="small" placeholder="100" />
          <n-button-group>
            <n-button size="small">元</n-button>
          </n-button-group>
        </n-input-group>
      </n-form-item>

      <n-form-item label="手续费" required>
        <n-input-group>
          <n-input-number v-model:value="formData.withdrawalFee" :min="0" :step="0.1" :precision="1" size="small" placeholder="0" />
          <n-button-group>
            <n-button size="small">%</n-button>
          </n-button-group>
        </n-input-group>
      </n-form-item>

      <n-form-item label="提现渠道" required>
        <n-radio-group v-model:value="formData.withdrawalChannel" size="small">
          <n-space>
            <n-radio value="alipay">支付宝</n-radio>
            <n-radio value="merchant">微信零钱</n-radio>
          </n-space>
        </n-radio-group>
      </n-form-item>
    </n-form>

    <div class="action-buttons">
      <n-button type="primary" size="small" @click="handleSave" :loading="saving">保存配置</n-button>
      <n-button size="small" @click="handleReset" :loading="resetting">重置配置</n-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import {
  NForm,
  NFormItem,
  NInputNumber,
  NButton,
  NButtonGroup,
  NInputGroup,
  NRadioGroup,
  NRadio,
  NSpace,
  useMessage
} from 'naive-ui'
import axios from 'axios'
import { useReferralStore } from './store'

const message = useMessage()
const saving = ref(false)
const resetting = ref(false)
const referralStore = useReferralStore()

// 定义组件类型属性
const type = 'referral'

const formData = reactive({
  inviteeReward: 30,
  promoterCashback: 5,
  promoterCommission: 2,
  minWithdrawal: 1,
  withdrawalChannel: 'alipay',
  withdrawalFee: 0
})

const handleSave = async () => {
  if (!formData.inviteeReward && formData.inviteeReward !== 0) {
    message.error('合伙人升级条件不能为空')
    return
  }
  
  saving.value = true
  
  try {
    // 准备符合API格式的数据 - 新的对象格式
    const apiData = {
      inviteeReward: formData.inviteeReward,
      promoterCashback: formData.promoterCashback,
      promoterCommission: formData.promoterCommission,
      minWithdrawal: formData.minWithdrawal,
      withdrawalChannel: formData.withdrawalChannel,
      withdrawalFee: formData.withdrawalFee
    }
    
    // 获取其他页面的配置数据
    const invitationData = referralStore.invitationData.value
    const shareData = referralStore.shareData.value

    // 合并所有配置数据 - 使用对象格式
    const fullApiData = {
      ...apiData,
      ...invitationData,
      ...shareData
    }

    // 尝试保存到API
    const response = await axios.post(`/api/config/frontend/${type}`, fullApiData, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
      }
    })
    
    message.success('配置保存成功')
    // 更新store中的合伙人配置数据
    referralStore.updatePartnerData(apiData)
    // 触发事件
    window.dispatchEvent(new CustomEvent('referral-config-saved'))
  } catch (error) {
    console.error('保存配置失败:', error)
    message.error('保存配置失败: ' + (error.message || '未知错误'))
  } finally {
    saving.value = false
  }
}

const handleReset = async () => {
  if (!window.confirm('确定要重置合伙人配置吗？这将清空所有已填写的合伙人配置值。')) {
    return
  }
  
  resetting.value = true
  try {
    Object.assign(formData, {
      inviteeReward: 30,
      promoterCashback: 5,
      promoterCommission: 2,
      minWithdrawal: 1,
      withdrawalChannel: 'alipay',
      withdrawalFee: 0
    })
    message.success('合伙人配置已重置')
  } catch (error) {
    console.error('重置配置失败:', error)
    message.error('重置配置失败: ' + (error.message || '未知错误'))
  } finally {
    resetting.value = false
  }
}

const loadConfig = async () => {
  try {
    // 从store中获取数据
    const storeData = referralStore.partnerData.value
    if (storeData && storeData.length > 0) {
      // 转换格式
      const configData = {}
      storeData.forEach(item => {
        configData[item.key] = item.value
      })
      
      Object.assign(formData, {
        inviteeReward: parseInt(configData.inviteeReward || '30'),
        promoterCashback: parseInt(configData.promoterCashback || '5'),
        promoterCommission: parseInt(configData.promoterCommission || '2'),
        minWithdrawal: parseInt(configData.minWithdrawal || '1'),
        withdrawalChannel: configData.withdrawalChannel || 'alipay',
        withdrawalFee: parseFloat(configData.withdrawalFee || '0')
      })
    } else {
      // 如果store中没有数据，从API加载
      await referralStore.loadAllConfig()
      
      // 等待加载完成后更新表单
      const configData = {}
      referralStore.partnerData.value.forEach(item => {
        configData[item.key] = item.value
      })
      
      Object.assign(formData, {
        inviteeReward: parseInt(configData.inviteeReward || '30'),
        promoterCashback: parseInt(configData.promoterCashback || '5'),
        promoterCommission: parseInt(configData.promoterCommission || '2'),
        minWithdrawal: parseInt(configData.minWithdrawal || '1'),
        withdrawalChannel: configData.withdrawalChannel || 'alipay',
        withdrawalFee: parseFloat(configData.withdrawalFee || '0')
      })
    }
  } catch (error) {
    console.warn('加载合伙人配置失败:', error)
    message.error('加载配置失败: ' + (error.message || '未知错误'))
  }
}

onMounted(() => {
  loadConfig()
})

// 对外暴露方法
defineExpose({
  loadConfig
})
</script>

<style scoped>
.partner-config {
  padding: 12px;
}

.action-buttons {
  margin-top: 16px;
  display: flex;
  gap: 8px;
}
</style>