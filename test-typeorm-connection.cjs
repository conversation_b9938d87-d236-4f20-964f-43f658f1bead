// 测试TypeORM连接和实体查询
const { DataSource } = require('./server/node_modules/typeorm');

// 模拟ConfigSystem实体
class ConfigSystem {
  constructor() {
    this.id = 0;
    this.siteName = '';
    this.siteLogo = '';
    this.siteDescription = '';
    this.copyright = '';
    this.contactEmail = '';
    this.beianInfo = '';
    this.siteUrl = '';
    this.maxLoginAttempts = 5;
    this.loginLockTime = 30;
    this.sessionTimeout = 120;
    this.maintenanceMode = false;
    this.primaryColor = '#1890ff';
    this.createdAt = new Date();
    this.updatedAt = new Date();
  }
}

async function testTypeORMConnection() {
  try {
    console.log('创建数据源连接...');
    
    const AppDataSource = new DataSource({
      type: 'mysql',
      host: 'localhost',
      port: 3306,
      username: 'root',
      password: 'root',
      database: 'ai_agent',
      synchronize: false,
      logging: true, // 启用日志查看SQL
      charset: 'utf8mb4',
      extra: {
        charset: 'utf8mb4_unicode_ci'
      },
      entities: [] // 暂时不加载实体
    });

    await AppDataSource.initialize();
    console.log('数据源连接成功');

    // 直接执行SQL查询
    console.log('执行原始SQL查询...');
    const result = await AppDataSource.query('SELECT * FROM config_system ORDER BY id ASC LIMIT 1');
    console.log('SQL查询结果:', result);

    // 测试使用QueryBuilder
    console.log('测试QueryBuilder...');
    const qbResult = await AppDataSource
      .createQueryBuilder()
      .select('*')
      .from('config_system', 'cs')
      .orderBy('cs.id', 'ASC')
      .limit(1)
      .getRawOne();
    
    console.log('QueryBuilder结果:', qbResult);

    await AppDataSource.destroy();
    console.log('连接已关闭');
  } catch (error) {
    console.error('测试失败:', error);
  }
}

testTypeORMConnection();
