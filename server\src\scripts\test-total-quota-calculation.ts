import { AppDataSource } from '../data-source';

async function testTotalQuotaCalculation() {
  console.log('🧪 测试总额度计算逻辑...\n');

  try {
    // 初始化数据库连接
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
      console.log('✅ 数据库连接已建立\n');
    }

    // 导入DailyLimitService
    const { DailyLimitService } = await import('../services/daily-limit.service');

    // 获取测试用户
    const users = await AppDataSource.query(`
      SELECT id, name, points, packageId, packageExpiredAt
      FROM members 
      WHERE id IN (59, 60, 61)
      ORDER BY id ASC
    `);

    for (const user of users) {
      console.log(`\n=== 用户 ${user.id} (${user.name}) ===`);
      console.log(`💰 数据库中的总点数: ${user.points} 点数`);

      try {
        // 获取分类额度详情
        const quotaDetails = await DailyLimitService.getQuotaDetailsByCategory(user.id);
        
        console.log('\n📋 各类额度剩余:');
        
        let totalRemaining = 0;
        
        // 1. 免费额度剩余
        const freeRemaining = Math.max(0, (quotaDetails.freeQuota.dailyLimit || 20) - (quotaDetails.freeQuota.todayUsed || 0));
        console.log(`🎁 免费额度: ${freeRemaining} 点数`);
        totalRemaining += freeRemaining;

        // 2. 限制套餐剩余
        if (quotaDetails.limitedPackages.length > 0) {
          quotaDetails.limitedPackages.forEach(pkg => {
            const packageRemaining = Math.max(0, pkg.dailyLimit - (pkg.todayUsed || 0));
            console.log(`💎 ${pkg.title}: ${packageRemaining} 点数`);
            totalRemaining += packageRemaining;
          });
        }

        // 3. 无限制套餐剩余 (无限制不计入总数)
        if (quotaDetails.unlimitedPackages.length > 0) {
          quotaDetails.unlimitedPackages.forEach(pkg => {
            console.log(`🚀 ${pkg.title}: 无限制 (不计入总数)`);
          });
        }

        // 4. 无套餐情况
        if (quotaDetails.limitedPackages.length === 0 && quotaDetails.unlimitedPackages.length === 0) {
          console.log('📦 套餐额度: 暂无有效套餐');
        }

        console.log(`\n🧮 计算结果:`);
        console.log(`   各类额度剩余总和: ${totalRemaining} 点数`);
        console.log(`   数据库总点数: ${user.points} 点数`);
        
        if (quotaDetails.unlimitedPackages.length > 0) {
          console.log(`   ✨ 注意: 该用户有无限制套餐，实际可用额度为无限制`);
        } else {
          const difference = user.points - totalRemaining;
          if (difference === 0) {
            console.log(`   ✅ 数据一致！`);
          } else {
            console.log(`   ⚠️ 差异: ${difference} 点数`);
            console.log(`   💡 可能原因: 点数余额包含了已消费但未重置的日限额`);
          }
        }

      } catch (error) {
        console.error(`❌ 获取用户 ${user.id} 额度详情失败:`, error);
      }
    }

    console.log('\n✅ 总额度计算测试完成！');
    
    console.log('\n📋 新显示逻辑:');
    console.log('1. 去掉顶部总点数余额显示');
    console.log('2. 只显示各类额度的剩余点数');
    console.log('3. 免费额度 + 限制套餐额度 = 可用的有限额度');
    console.log('4. 无限制套餐单独显示，不计入总数');
    console.log('5. 用户可以清楚看到每种额度类型的剩余情况');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    // 关闭数据库连接
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('\n🔌 数据库连接已关闭');
    }
  }
}

// 运行测试
testTotalQuotaCalculation().catch(console.error);
