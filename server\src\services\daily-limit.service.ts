import { AppDataSource } from '../data-source';
import { ConfigFreeQuota } from '../entity/ConfigFreeQuota';

/**
 * 每日限制检查服务
 * 用于检查用户当天的使用额度是否超过限制
 */
export class DailyLimitService {

  /**
   * 检查用户是否超过每日使用限制
   * @param memberId 用户ID
   * @param requiredPoints 本次操作需要的点数
   * @returns Promise<{allowed: boolean, message?: string, todayUsage?: number, dailyLimit?: number}>
   */
  static async checkDailyLimit(memberId: number, requiredPoints: number = 1): Promise<{
    allowed: boolean;
    message?: string;
    todayUsage?: number;
    dailyLimit?: number;
    isVipUser?: boolean;
  }> {
    try {
      console.log(`🔍 检查用户 ${memberId} 的每日使用限制，需要点数: ${requiredPoints}`);

      // 1. 获取用户的套餐信息
      const userPackageInfo = await this.getUserPackageInfo(memberId);

      if (!userPackageInfo.exists) {
        console.log(`❌ 用户 ${memberId} 不存在`);
        return {
          allowed: false,
          message: '用户不存在'
        };
      }

      // 2. 计算用户的每日限制额度
      const dailyLimitInfo = await this.calculateDailyLimit(memberId, userPackageInfo);

      // 3. 获取今日已使用的点数
      const todayUsage = await this.getTodayUsagePoints(memberId);

      console.log(`📊 用户 ${memberId} 今日使用情况: ${todayUsage}/${dailyLimitInfo.dailyLimit} 点数`);
      console.log(`🎯 套餐信息:`, dailyLimitInfo);

      // 4. 检查是否超过限制
      if (dailyLimitInfo.isUnlimited) {
        console.log(`✅ 用户 ${memberId} 有无限制套餐，允许使用`);
        return {
          allowed: true,
          todayUsage,
          dailyLimit: -1, // -1 表示无限制
          isVipUser: true
        };
      }

      if (todayUsage + requiredPoints > dailyLimitInfo.dailyLimit) {
        console.log(`❌ 用户 ${memberId} 今日使用额度已达上限`);
        return {
          allowed: false,
          message: dailyLimitInfo.message || '您今日的使用额度已用完，请购买套餐获取更多额度',
          todayUsage,
          dailyLimit: dailyLimitInfo.dailyLimit,
          isVipUser: dailyLimitInfo.hasValidPackage
        };
      }

      console.log(`✅ 用户 ${memberId} 今日使用额度未超限，允许使用`);
      return {
        allowed: true,
        todayUsage,
        dailyLimit: dailyLimitInfo.dailyLimit,
        isVipUser: dailyLimitInfo.hasValidPackage
      };

    } catch (error) {
      console.error('检查每日限制失败:', error);
      // 出错时为了安全起见，不允许使用，避免产生不必要的费用
      return {
        allowed: false,
        message: '系统暂时无法验证您的使用额度，请稍后再试'
      };
    }
  }

  /**
   * 获取用户套餐信息
   */
  private static async getUserPackageInfo(memberId: number): Promise<{
    exists: boolean;
    hasValidPackage: boolean;
    packageId?: number;
    packageExpiredAt?: Date;
  }> {
    try {
      const memberInfo = await AppDataSource.query(`
        SELECT packageId, packageExpiredAt
        FROM members
        WHERE id = ?
      `, [memberId]);

      if (memberInfo.length === 0) {
        return { exists: false, hasValidPackage: false };
      }

      const member = memberInfo[0];
      const hasValidPackage = member.packageId &&
                             member.packageExpiredAt &&
                             new Date(member.packageExpiredAt) > new Date();

      return {
        exists: true,
        hasValidPackage,
        packageId: member.packageId,
        packageExpiredAt: member.packageExpiredAt
      };
    } catch (error) {
      console.error('获取用户套餐信息失败:', error);
      return { exists: false, hasValidPackage: false };
    }
  }

  /**
   * 计算用户的每日限制额度
   */
  private static async calculateDailyLimit(memberId: number, userPackageInfo: any): Promise<{
    dailyLimit: number;
    isUnlimited: boolean;
    hasValidPackage: boolean;
    message?: string;
  }> {
    try {
      // 如果用户有有效套餐，获取套餐的每日限制
      if (userPackageInfo.hasValidPackage) {
        const packageInfo = await AppDataSource.query(`
          SELECT dailyMaxConsumption, title
          FROM package
          WHERE id = ?
        `, [userPackageInfo.packageId]);

        if (packageInfo.length > 0) {
          const pkg = packageInfo[0];
          const dailyMaxConsumption = pkg.dailyMaxConsumption;

          // 如果套餐的每日限制为0或-1，表示无限制
          if (dailyMaxConsumption === 0 || dailyMaxConsumption === -1) {
            console.log(`✅ 用户套餐 "${pkg.title}" 为无限制套餐`);
            return {
              dailyLimit: -1,
              isUnlimited: true,
              hasValidPackage: true
            };
          }

          console.log(`📦 用户套餐 "${pkg.title}" 每日限制: ${dailyMaxConsumption} 点数`);
          return {
            dailyLimit: dailyMaxConsumption,
            isUnlimited: false,
            hasValidPackage: true,
            message: `您的套餐每日可使用 ${dailyMaxConsumption} 点数，今日额度已用完`
          };
        }
      }

      // 免费用户，使用免费额度配置
      const config = await this.getFreeQuotaConfig();
      if (!config.enabled) {
        return {
          dailyLimit: -1,
          isUnlimited: true,
          hasValidPackage: false
        };
      }

      return {
        dailyLimit: config.maxConsumePerSession,
        isUnlimited: config.maxConsumePerSession === 0,
        hasValidPackage: false,
        message: config.insufficientQuotaMessage
      };

    } catch (error) {
      console.error('计算每日限制失败:', error);
      // 默认返回免费额度
      return {
        dailyLimit: 20,
        isUnlimited: false,
        hasValidPackage: false,
        message: '您今日的免费使用额度已用完，请购买套餐获取更多额度'
      };
    }
  }

  /**
   * 获取免费额度配置
   */
  private static async getFreeQuotaConfig(): Promise<{
    enabled: boolean;
    maxConsumePerSession: number;
    insufficientQuotaMessage: string;
  }> {
    try {
      const configRepository = AppDataSource.getRepository(ConfigFreeQuota);
      const config = await configRepository.findOne({
        where: {},
        order: { id: 'ASC' }
      });

      if (config) {
        const result = {
          enabled: config.enabled,
          maxConsumePerSession: config.maxConsumePerSession,
          insufficientQuotaMessage: config.insufficientQuotaMessage
        };
        console.log('📋 免费额度配置:', result);
        return result;
      } else {
        // 如果没有配置，创建默认配置
        const defaultConfig = configRepository.create({
          enabled: true,
          newUserQuota: 100,
          quotaValidDays: 30,
          maxConsumePerSession: 20,
          insufficientQuotaMessage: '您今日的免费使用额度已用完，请购买套餐获取更多额度'
        });
        await configRepository.save(defaultConfig);

        const result = {
          enabled: defaultConfig.enabled,
          maxConsumePerSession: defaultConfig.maxConsumePerSession,
          insufficientQuotaMessage: defaultConfig.insufficientQuotaMessage
        };
        console.log('📋 创建默认免费额度配置:', result);
        return result;
      }
    } catch (error) {
      console.error('获取免费额度配置失败:', error);
      // 返回默认配置
      return {
        enabled: true,
        maxConsumePerSession: 20,
        insufficientQuotaMessage: '您今日的免费使用额度已用完，请购买套餐获取更多额度'
      };
    }
  }

  /**
   * 获取用户今天的使用点数
   */
  private static async getTodayUsagePoints(memberId: number): Promise<number> {
    try {
      // 统计今天的使用点数（聊天和工作流）
      const result = await AppDataSource.query(`
        SELECT COALESCE(SUM(ABS(amount)), 0) as totalPoints
        FROM points_records
        WHERE memberId = ?
        AND type = 'app_usage'
        AND amount < 0
        AND DATE(createdAt) = CURDATE()
      `, [memberId]);

      return result[0]?.totalPoints || 0;
    } catch (error) {
      console.error('获取今日使用点数失败:', error);
      return 0;
    }
  }

  /**
   * 获取用户按来源分类的额度详情
   */
  static async getQuotaDetailsByCategory(memberId: number) {
    try {
      console.log(`\n=== 获取用户 ${memberId} 的分类额度详情 ===`);

      // 获取今日使用点数
      const todayUsed = await this.getTodayUsagePoints(memberId);
      console.log('今日已使用点数:', todayUsed);

      // 1. 统计免费额度（注册赠送 + 邀请赠送）
      const freeQuotaStats = await this.getFreeQuotaStats(memberId);

      // 2. 统计限制套餐额度（package_purchase + redemption_code 中的限制套餐）
      const limitedPackageStats = await this.getLimitedPackageStats(memberId);

      // 3. 统计无限制套餐额度（package_purchase + redemption_code 中的无限制套餐）
      const unlimitedPackageStats = await this.getUnlimitedPackageStats(memberId);

      // 4. 获取免费额度配置
      const freeQuotaConfig = await this.getFreeQuotaConfig();
      const freeDailyLimit = freeQuotaConfig.maxConsumePerSession || 20;

      // 构建免费额度信息
      const freeQuota = {
        title: '免费额度',
        type: 'free',
        totalPoints: freeQuotaStats.totalPoints, // 总的免费点数
        dailyLimit: freeDailyLimit, // 每日限额
        todayUsed: Math.min(todayUsed, freeDailyLimit), // 今日已使用（优先消耗免费额度）
        remaining: Math.max(0, freeDailyLimit - todayUsed), // 今日剩余
        source: 'system',
        details: freeQuotaStats.details
      };

      // 构建限制套餐信息
      const limitedPackages = await this.buildLimitedPackageInfo(memberId, limitedPackageStats, todayUsed, freeDailyLimit);

      // 构建无限制套餐信息
      const unlimitedPackages = await this.buildUnlimitedPackageInfo(memberId, unlimitedPackageStats, todayUsed, freeDailyLimit);

      const result = {
        freeQuota,
        limitedPackages,
        unlimitedPackages,
        totalTodayUsed: todayUsed
      };

      console.log('分类额度详情结果:', JSON.stringify(result, null, 2));
      return result;

    } catch (error) {
      console.error('获取分类额度详情失败:', error);
      throw error;
    }
  }

  /**
   * 统计用户的免费额度（注册赠送 + 邀请赠送）
   */
  static async getFreeQuotaStats(memberId: number) {
    try {
      const freeRecords = await AppDataSource.query(`
        SELECT type, amount, description, createdAt
        FROM points_records
        WHERE memberId = ?
        AND type IN ('register_gift', 'invite_gift')
        AND amount > 0
        ORDER BY createdAt ASC
      `, [memberId]);

      let totalPoints = 0;
      const details = [];

      for (const record of freeRecords) {
        totalPoints += record.amount;
        details.push({
          type: record.type,
          amount: record.amount,
          description: record.description,
          date: record.createdAt
        });
      }

      return {
        totalPoints,
        details
      };
    } catch (error) {
      console.error('统计免费额度失败:', error);
      return { totalPoints: 0, details: [] };
    }
  }

  /**
   * 统计用户的限制套餐额度
   */
  static async getLimitedPackageStats(memberId: number) {
    try {
      // 获取套餐购买记录，并关联套餐信息判断是否为限制套餐
      const packageRecords = await AppDataSource.query(`
        SELECT pr.amount, pr.description, pr.createdAt, pr.relatedId, pr.metadata,
               p.title, p.dailyMaxConsumption
        FROM points_records pr
        LEFT JOIN package p ON pr.relatedId = p.id
        WHERE pr.memberId = ?
        AND pr.type IN ('package_purchase', 'redemption_code')
        AND pr.amount > 0
        AND (p.dailyMaxConsumption > 0 OR pr.relatedId IS NULL)
        ORDER BY pr.createdAt ASC
      `, [memberId]);

      let totalPoints = 0;
      const details = [];

      for (const record of packageRecords) {
        // 如果没有关联套餐信息，或者套餐有日限额，则认为是限制套餐
        if (!record.relatedId || (record.dailyMaxConsumption && record.dailyMaxConsumption > 0)) {
          totalPoints += record.amount;
          details.push({
            amount: record.amount,
            description: record.description,
            packageTitle: record.title || '未知套餐',
            dailyLimit: record.dailyMaxConsumption || 0,
            date: record.createdAt
          });
        }
      }

      return {
        totalPoints,
        details
      };
    } catch (error) {
      console.error('统计限制套餐额度失败:', error);
      return { totalPoints: 0, details: [] };
    }
  }

  /**
   * 统计用户的无限制套餐额度
   */
  static async getUnlimitedPackageStats(memberId: number) {
    try {
      // 获取无限制套餐购买记录
      const unlimitedRecords = await AppDataSource.query(`
        SELECT pr.amount, pr.description, pr.createdAt, pr.relatedId, pr.metadata,
               p.title, p.dailyMaxConsumption
        FROM points_records pr
        LEFT JOIN package p ON pr.relatedId = p.id
        WHERE pr.memberId = ?
        AND pr.type IN ('package_purchase', 'redemption_code')
        AND pr.amount > 0
        AND p.dailyMaxConsumption IN (0, -1)
        ORDER BY pr.createdAt ASC
      `, [memberId]);

      let totalPoints = 0;
      const details = [];

      for (const record of unlimitedRecords) {
        totalPoints += record.amount;
        details.push({
          amount: record.amount,
          description: record.description,
          packageTitle: record.title || '未知套餐',
          date: record.createdAt
        });
      }

      return {
        totalPoints,
        details
      };
    } catch (error) {
      console.error('统计无限制套餐额度失败:', error);
      return { totalPoints: 0, details: [] };
    }
  }

  /**
   * 获取今日免费额度使用情况
   */
  static async getTodayFreeQuotaUsage(memberId: number): Promise<number> {
    try {
      const result = await AppDataSource.query(`
        SELECT COALESCE(SUM(ABS(amount)), 0) as totalPoints
        FROM points_records
        WHERE memberId = ?
        AND type = 'app_usage'
        AND amount < 0
        AND DATE(createdAt) = CURDATE()
        AND (description LIKE '%免费额度%' OR description LIKE '%free%')
      `, [memberId]);

      return parseInt(result[0]?.totalPoints || 0);
    } catch (error) {
      console.error('获取今日免费额度使用失败:', error);
      return 0;
    }
  }

  /**
   * 获取今日特定套餐使用情况
   */
  static async getTodayPackageUsage(memberId: number, packageId: number): Promise<number> {
    try {
      const result = await AppDataSource.query(`
        SELECT COALESCE(SUM(ABS(amount)), 0) as totalPoints
        FROM points_records
        WHERE memberId = ?
        AND type = 'app_usage'
        AND amount < 0
        AND DATE(createdAt) = CURDATE()
        AND (description LIKE '%套餐%' OR description LIKE CONCAT('%package_', ?))
      `, [memberId, packageId]);

      return parseInt(result[0]?.totalPoints || 0);
    } catch (error) {
      console.error('获取今日套餐使用失败:', error);
      return 0;
    }
  }

  /**
   * 构建限制套餐信息
   */
  static async buildLimitedPackageInfo(memberId: number, limitedPackageStats: any, todayUsed: number, freeDailyLimit: number) {
    try {
      const limitedPackages = [];

      // 获取用户当前套餐信息
      const userPackageInfo = await AppDataSource.query(`
        SELECT m.packageId, m.packageExpiredAt, p.title as currentTitle, p.dailyMaxConsumption as currentDailyMax
        FROM members m
        LEFT JOIN package p ON m.packageId = p.id
        WHERE m.id = ?
      `, [memberId]);

      const currentUser = userPackageInfo[0];
      const hasValidPackage = currentUser?.packageId &&
                             currentUser?.packageExpiredAt &&
                             new Date(currentUser.packageExpiredAt) > new Date();

      // 如果用户当前有有效的限制套餐，显示当前套餐信息
      if (hasValidPackage && currentUser.currentDailyMax > 0) {
        // 计算套餐使用量（在免费额度用完后才使用套餐额度）
        const packageUsed = Math.max(0, todayUsed - freeDailyLimit);

        limitedPackages.push({
          title: currentUser.currentTitle,
          type: 'limited',
          totalPoints: limitedPackageStats.totalPoints, // 总购买的限制套餐点数
          dailyLimit: currentUser.currentDailyMax,
          todayUsed: Math.min(packageUsed, currentUser.currentDailyMax),
          remaining: Math.max(0, currentUser.currentDailyMax - packageUsed),
          expiredAt: currentUser.packageExpiredAt,
          source: 'package',
          isActive: true
        });
      } else if (limitedPackageStats.totalPoints > 0) {
        // 如果用户没有当前有效的限制套餐，但购买过限制套餐，显示历史购买信息
        limitedPackages.push({
          title: '限制套餐（历史购买）',
          type: 'limited',
          totalPoints: limitedPackageStats.totalPoints,
          dailyLimit: 0, // 没有当前有效套餐，日限额为0
          todayUsed: 0,
          remaining: 0,
          expiredAt: null,
          source: 'package',
          isActive: false,
          note: '您曾购买过限制套餐，但当前没有有效的限制套餐'
        });
      }

      return limitedPackages;
    } catch (error) {
      console.error('构建限制套餐信息失败:', error);
      return [];
    }
  }

  /**
   * 构建无限制套餐信息
   */
  static async buildUnlimitedPackageInfo(memberId: number, unlimitedPackageStats: any, todayUsed: number, freeDailyLimit: number) {
    try {
      const unlimitedPackages = [];

      // 获取当前有效的无限制套餐
      const activeUnlimitedPackages = await AppDataSource.query(`
        SELECT p.id, p.title, p.dailyMaxConsumption, m.packageExpiredAt
        FROM members m
        LEFT JOIN package p ON m.packageId = p.id
        WHERE m.id = ?
        AND m.packageId IS NOT NULL
        AND m.packageExpiredAt > NOW()
        AND p.dailyMaxConsumption IN (0, -1)
      `, [memberId]);

      for (const pkg of activeUnlimitedPackages) {
        // 无限制套餐的使用量（在免费额度和限制套餐用完后使用）
        const unlimitedUsed = Math.max(0, todayUsed - freeDailyLimit);

        unlimitedPackages.push({
          title: pkg.title,
          type: 'unlimited',
          totalPoints: unlimitedPackageStats.totalPoints, // 总购买的无限制套餐点数
          dailyLimit: -1, // 无限制
          todayUsed: unlimitedUsed,
          remaining: -1, // 无限制
          expiredAt: pkg.packageExpiredAt,
          source: 'package'
        });
      }

      return unlimitedPackages;
    } catch (error) {
      console.error('构建无限制套餐信息失败:', error);
      return [];
    }
  }

  /**
   * 获取用户今天的详细使用统计 (保持向后兼容)
   */
  static async getTodayUsageStats(memberId: number): Promise<{
    totalUsage: number;
    chatUsage: number;
    workflowUsage: number;
    dailyLimit: number;
    remaining: number;
    isVipUser: boolean;
    packageTitle?: string;
    packageExpiredAt?: string;
    freeQuotaConfig?: any;
  }> {
    try {
      // 获取用户套餐信息
      const userPackageInfo = await this.getUserPackageInfo(memberId);

      if (!userPackageInfo.exists) {
        return {
          totalUsage: 0,
          chatUsage: 0,
          workflowUsage: 0,
          dailyLimit: 0,
          remaining: 0,
          isVipUser: false
        };
      }

      // 计算每日限制
      const dailyLimitInfo = await this.calculateDailyLimit(memberId, userPackageInfo);

      if (dailyLimitInfo.isUnlimited) {
        // 获取套餐详细信息
        let packageTitle = '';
        let packageExpiredAt = '';
        if (userPackageInfo.hasValidPackage && userPackageInfo.packageId) {
          try {
            const packageInfo = await AppDataSource.query(`
              SELECT title
              FROM package
              WHERE id = ?
            `, [userPackageInfo.packageId]);

            if (packageInfo.length > 0) {
              packageTitle = packageInfo[0].title;
            }

            if (userPackageInfo.packageExpiredAt) {
              packageExpiredAt = userPackageInfo.packageExpiredAt.toISOString();
            }
          } catch (error) {
            console.error('获取套餐详细信息失败:', error);
          }
        }

        // 获取免费额度配置
        const freeQuotaConfig = await this.getFreeQuotaConfig();

        return {
          totalUsage: 0,
          chatUsage: 0,
          workflowUsage: 0,
          dailyLimit: -1, // -1 表示无限制
          remaining: -1,
          isVipUser: dailyLimitInfo.hasValidPackage,
          packageTitle,
          packageExpiredAt,
          freeQuotaConfig
        };
      }

      // 统计今天的使用点数
      const totalUsage = await this.getTodayUsagePoints(memberId);

      // 统计聊天使用点数
      const chatResult = await AppDataSource.query(`
        SELECT COALESCE(SUM(ABS(amount)), 0) as totalPoints
        FROM points_records
        WHERE memberId = ?
        AND type = 'app_usage'
        AND amount < 0
        AND DATE(createdAt) = CURDATE()
        AND (description LIKE '%聊天%' OR description LIKE '%chat%')
      `, [memberId]);

      // 统计工作流使用点数
      const workflowResult = await AppDataSource.query(`
        SELECT COALESCE(SUM(ABS(amount)), 0) as totalPoints
        FROM points_records
        WHERE memberId = ?
        AND type = 'app_usage'
        AND amount < 0
        AND DATE(createdAt) = CURDATE()
        AND (description LIKE '%工作流%' OR description LIKE '%workflow%')
      `, [memberId]);

      const chatUsage = chatResult[0]?.totalPoints || 0;
      const workflowUsage = workflowResult[0]?.totalPoints || 0;
      const remaining = Math.max(0, dailyLimitInfo.dailyLimit - totalUsage);

      // 获取套餐详细信息
      let packageTitle = '';
      let packageExpiredAt = '';
      if (userPackageInfo.hasValidPackage && userPackageInfo.packageId) {
        try {
          const packageInfo = await AppDataSource.query(`
            SELECT title
            FROM package
            WHERE id = ?
          `, [userPackageInfo.packageId]);

          if (packageInfo.length > 0) {
            packageTitle = packageInfo[0].title;
          }

          if (userPackageInfo.packageExpiredAt) {
            packageExpiredAt = userPackageInfo.packageExpiredAt.toISOString();
          }
        } catch (error) {
          console.error('获取套餐详细信息失败:', error);
        }
      }

      // 获取免费额度配置
      const freeQuotaConfig = await this.getFreeQuotaConfig();

      return {
        totalUsage,
        chatUsage,
        workflowUsage,
        dailyLimit: dailyLimitInfo.dailyLimit,
        remaining,
        isVipUser: dailyLimitInfo.hasValidPackage,
        packageTitle,
        packageExpiredAt,
        freeQuotaConfig
      };

    } catch (error) {
      console.error('获取今日使用统计失败:', error);
      return {
        totalUsage: 0,
        chatUsage: 0,
        workflowUsage: 0,
        dailyLimit: 20,
        remaining: 20,
        isVipUser: false
      };
    }
  }
}
