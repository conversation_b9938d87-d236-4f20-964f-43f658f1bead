import { AppDataSource } from '../data-source';

async function testQuotaDetailsAPI() {
  console.log('🧪 开始测试额度详情API...\n');

  try {
    // 初始化数据库连接
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
      console.log('✅ 数据库连接已建立\n');
    }

    // 获取测试用户的token
    console.log('1. 获取测试用户信息...');
    const users = await AppDataSource.query(`
      SELECT id, name, points, balance, packageId, packageExpiredAt
      FROM members 
      WHERE id IN (59, 60)
      ORDER BY id ASC
    `);

    console.log('👥 测试用户:');
    users.forEach((user: any) => {
      const hasValidPackage = user.packageId && user.packageExpiredAt && new Date(user.packageExpiredAt) > new Date();
      console.log(`  ${user.id}. ${user.name} - 余额: ¥${user.balance}, 点数: ${user.points}, 套餐: ${user.packageId || '无'} ${hasValidPackage ? '(有效)' : '(无效/过期)'}`);
    });

    // 模拟API请求测试
    console.log('\n2. 测试额度详情API...');
    
    // 导入DailyLimitService进行测试
    const { DailyLimitService } = await import('../services/daily-limit.service');

    for (const user of users) {
      console.log(`\n=== 测试用户 ${user.id} (${user.name}) ===`);
      
      try {
        const stats = await DailyLimitService.getTodayUsageStats(user.id);
        
        console.log('📊 额度详情:');
        console.log(`  总使用: ${stats.totalUsage} 点数`);
        console.log(`  聊天使用: ${stats.chatUsage} 点数`);
        console.log(`  工作流使用: ${stats.workflowUsage} 点数`);
        console.log(`  每日限制: ${stats.dailyLimit === -1 ? '无限制' : stats.dailyLimit + ' 点数'}`);
        console.log(`  剩余额度: ${stats.remaining === -1 ? '无限制' : stats.remaining + ' 点数'}`);
        console.log(`  VIP用户: ${stats.isVipUser ? '是' : '否'}`);
        
        if (stats.packageTitle) {
          console.log(`  套餐名称: ${stats.packageTitle}`);
        }
        
        if (stats.packageExpiredAt) {
          const expiredDate = new Date(stats.packageExpiredAt);
          console.log(`  套餐到期: ${expiredDate.toLocaleString()}`);
        }
        
        if (stats.freeQuotaConfig) {
          console.log(`  免费额度配置:`);
          console.log(`    启用状态: ${stats.freeQuotaConfig.enabled}`);
          console.log(`    每日限制: ${stats.freeQuotaConfig.maxConsumePerSession} 点数`);
          console.log(`    不足提示: ${stats.freeQuotaConfig.insufficientQuotaMessage}`);
        }
        
      } catch (error) {
        console.error(`❌ 获取用户 ${user.id} 额度详情失败:`, error);
      }
    }

    console.log('\n3. 测试HTTP API端点...');
    
    // 这里我们可以模拟HTTP请求，但由于我们在脚本环境中，直接调用服务即可
    console.log('ℹ️ HTTP API端点: GET /api/daily-limit/stats');
    console.log('ℹ️ 需要在前端或Postman中测试完整的HTTP请求');

    console.log('\n✅ 额度详情API测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    // 关闭数据库连接
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('\n🔌 数据库连接已关闭');
    }
  }
}

// 运行测试
testQuotaDetailsAPI().catch(console.error);
