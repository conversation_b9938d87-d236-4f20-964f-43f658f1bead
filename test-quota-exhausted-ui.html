<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>额度用完UI测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .chat-container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .message {
            display: flex;
            margin-bottom: 16px;
            align-items: flex-start;
            gap: 12px;
        }
        
        .avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #007AFF;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            flex-shrink: 0;
        }
        
        .message-content {
            flex: 1;
        }
        
        .message-bubble {
            padding: 12px 16px;
            border-radius: 18px;
            max-width: 280px;
            word-wrap: break-word;
        }
        
        .user-bubble {
            background: #007AFF;
            color: white;
            margin-left: auto;
        }
        
        .assistant-bubble {
            background: #f0f0f0;
            color: #333;
        }
        
        /* 额度用完消息样式 */
        .quota-exhausted-bubble {
            background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%) !important;
            border: 1px solid #ffcc80;
            box-shadow: 0 2px 8px rgba(255, 152, 0, 0.1);
        }
        
        .quota-exhausted-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            gap: 12px;
        }
        
        .quota-icon {
            font-size: 24px;
            margin-bottom: 4px;
        }
        
        .quota-message {
            font-size: 14px;
            color: #e65100;
            line-height: 1.4;
            margin-bottom: 8px;
        }
        
        .quota-actions {
            width: 100%;
            display: flex;
            justify-content: center;
        }
        
        .quota-btn {
            background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            display: flex;
            align-items: center;
            gap: 6px;
            box-shadow: 0 2px 6px rgba(255, 152, 0, 0.3);
            transition: all 0.3s ease;
            cursor: pointer;
            border: none;
            font-family: inherit;
        }
        
        .quota-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(255, 152, 0, 0.4);
        }
        
        .quota-btn:active {
            transform: scale(0.95);
            box-shadow: 0 1px 3px rgba(255, 152, 0, 0.4);
        }
        
        .quota-btn-text {
            font-size: 13px;
            font-weight: 600;
        }
        
        .quota-btn-icon {
            font-size: 12px;
            font-weight: bold;
        }
        
        .test-info {
            background: #e3f2fd;
            padding: 16px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
        
        .test-info h3 {
            margin: 0 0 8px 0;
            color: #1976d2;
        }
        
        .test-info p {
            margin: 0;
            color: #424242;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="test-info">
        <h3>🧪 额度用完UI测试</h3>
        <p>这是前端显示额度用完消息的UI效果预览。当用户额度不足时，会在聊天框内显示这样的提示消息和购买按钮。</p>
    </div>
    
    <div class="chat-container">
        <!-- 用户消息 -->
        <div class="message">
            <div class="message-content" style="margin-left: auto;">
                <div class="message-bubble user-bubble">
                    帮我写一个健康饮食计划
                </div>
            </div>
            <div class="avatar">👤</div>
        </div>
        
        <!-- 额度用完的特殊消息 -->
        <div class="message">
            <div class="avatar">🤖</div>
            <div class="message-content">
                <div class="message-bubble quota-exhausted-bubble">
                    <div class="quota-exhausted-content">
                        <div class="quota-icon">⏰</div>
                        <div class="quota-message">您今日的免费使用额度已用完，请购买套餐获取更多额度</div>
                        <div class="quota-actions">
                            <button class="quota-btn" onclick="handleBuyPackage()">
                                <span class="quota-btn-text">购买套餐</span>
                                <span class="quota-btn-icon">→</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 另一个示例：VIP用户额度用完 -->
        <div class="message">
            <div class="message-content" style="margin-left: auto;">
                <div class="message-bubble user-bubble">
                    再帮我优化一下这个计划
                </div>
            </div>
            <div class="avatar">👤</div>
        </div>
        
        <div class="message">
            <div class="avatar">🤖</div>
            <div class="message-content">
                <div class="message-bubble quota-exhausted-bubble">
                    <div class="quota-exhausted-content">
                        <div class="quota-icon">⏰</div>
                        <div class="quota-message">您的套餐每日可使用 100 点数，今日额度已用完</div>
                        <div class="quota-actions">
                            <button class="quota-btn" onclick="handleBuyPackage()">
                                <span class="quota-btn-text">购买套餐</span>
                                <span class="quota-btn-icon">→</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function handleBuyPackage() {
            alert('🛒 跳转到购买套餐页面\n\n在实际应用中，这里会跳转到会员套餐页面，用户可以选择合适的套餐进行购买。');
        }
    </script>
</body>
</html>
