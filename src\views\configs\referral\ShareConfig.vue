<template>
  <div class="share-config">
    <n-form :model="formData" label-placement="left" label-width="80px" size="small">
      <n-form-item label="分享标题" required>
        <n-input v-model:value="formData.shareTitle" size="small" placeholder="分享标题" />
      </n-form-item>

      <n-form-item label="分享描述" required>
        <n-input
          v-model:value="formData.shareDescription"
          type="textarea"
          size="small"
          placeholder="分享描述"
          :autosize="{
            minRows: 2,
            maxRows: 3
          }"
        />
      </n-form-item>

      <n-form-item label="分享图片" required>
        <div class="upload-container">
          <div class="preview-wrapper">
            <div v-if="!formData.shareImageUrl" class="empty-image">
              <n-icon size="20"><ImageOutline /></n-icon>
            </div>
            <img v-else :src="formData.shareImageUrl" class="preview-image" />
          </div>
          <n-button type="primary" size="small" class="select-btn" @click="openMediaSelector">选择图片</n-button>
        </div>
      </n-form-item>

      <n-form-item label="邀请海报">
        <div class="poster-container">
          <div class="poster-preview-card">
            <div
              class="poster-preview-image"
              :style="{ backgroundImage: posterPreviewBackground }"
            >
              <div v-if="!formData.posterConfig.backgroundImage" class="empty-poster">
                <n-icon size="24"><ImageOutline /></n-icon>
                <div>暂无海报</div>
              </div>
              <div
                v-else
                class="qr-preview"
                :style="{
                  width: `${Math.round(formData.posterConfig.qrCodeSize * 0.3)}px`,
                  height: `${Math.round(formData.posterConfig.qrCodeSize * 0.3)}px`,
                  left: `${Math.round(formData.posterConfig.qrCodePosition.x * 0.3)}px`,
                  top: `${Math.round(formData.posterConfig.qrCodePosition.y * 0.3)}px`
                }"
              >
                二维码
              </div>
            </div>
          </div>
          <div class="poster-actions">
            <n-button type="primary" size="small" @click="openPosterEditor">设置海报</n-button>
          </div>
        </div>
      </n-form-item>
    </n-form>

    <div class="action-buttons">
      <n-button type="primary" size="small" @click="handleSave" :loading="saving">保存配置</n-button>
      <n-button size="small" @click="handleReset" :loading="resetting">重置配置</n-button>
    </div>

    <!-- 媒体选择器 -->
    <MediaSelectorModal
      v-model:show="showMediaSelector"
      type="image"
      @select="handleMediaSelected"
    />

    <!-- 海报编辑器 -->
    <ReferralPosterModal
      v-model:show="showPosterEditor"
      :poster-config="formData.posterConfig"
      @update:poster-config="handlePosterConfigUpdate"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import {
  NForm,
  NFormItem,
  NInput,
  NButton,
  NIcon,
  NText,
  useMessage
} from 'naive-ui'
import { ImageOutline } from '@vicons/ionicons5'
import axios from 'axios'
import MediaSelectorModal from '@/components/MediaSelectorModal.vue'
import ReferralPosterModal from '@/components/ReferralPosterModal.vue'
import { useReferralStore } from './store'
import { saveConfig } from '@/api/config'

const message = useMessage()
const saving = ref(false)
const resetting = ref(false)
const referralStore = useReferralStore()

// 媒体选择器相关
const showMediaSelector = ref(false)

// 海报编辑器相关
const showPosterEditor = ref(false)

// 定义组件类型属性
const type = 'referral'

const formData = reactive({
  shareTitle: 'AI智能体使用邀请',
  shareDescription: '注册即可获得额外免费使用次数，快来体验智能AI助手！',
  shareImageUrl: '',
  posterConfig: {
    backgroundImage: '',
    qrCodeSize: 202,
    qrCodePosition: { x: 0, y: 0 }
  }
})

// 海报预览背景计算属性
const posterPreviewBackground = computed(() => {
  if (formData.posterConfig.backgroundImage) {
    return `url(${formData.posterConfig.backgroundImage})`
  }
  return 'none'
})

const handleSave = async () => {
  if (!formData.shareTitle) {
    message.error('分享标题不能为空')
    return
  }
  
  if (!formData.shareDescription) {
    message.error('分享描述不能为空')
    return
  }
  
  saving.value = true
  
  try {
    // 准备符合API格式的数据 - 新的对象格式
    const apiData = {
      shareTitle: formData.shareTitle,
      shareDescription: formData.shareDescription,
      shareImageUrl: formData.shareImageUrl,
      posterBackgroundImage: formData.posterConfig.backgroundImage,
      posterQrCodeSize: formData.posterConfig.qrCodeSize,
      posterQrCodePositionX: formData.posterConfig.qrCodePosition.x,
      posterQrCodePositionY: formData.posterConfig.qrCodePosition.y
    }

    // 获取其他页面的配置数据
    const invitationData = referralStore.invitationData.value
    const partnerData = referralStore.partnerData.value

    // 合并所有配置数据 - 使用对象格式
    const fullApiData = {
      ...apiData,
      ...invitationData,
      ...partnerData
    }

    // 使用新的统一配置API端点
    const response = await axios.post(`/api/config/frontend/${type}`, fullApiData, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
      }
    })

    if (response.data && response.data.code === 200) {
      message.success('配置保存成功')
      // 更新store中的分享配置数据
      referralStore.updateShareData(apiData)
      // 触发事件
      window.dispatchEvent(new CustomEvent('referral-config-saved'))
    } else {
      throw new Error(response.data?.message || '保存失败')
    }
  } catch (error) {
    console.error('保存配置失败:', error)
    console.error('错误详情:', error.response?.data)
    const errorMessage = error.response?.data?.message || error.message || '未知错误'
    message.error('保存配置失败: ' + errorMessage)
  } finally {
    saving.value = false
  }
}

const handleReset = async () => {
  if (!window.confirm('确定要重置分享配置吗？这将清空所有已填写的分享配置值。')) {
    return
  }
  
  resetting.value = true
  try {
    Object.assign(formData, {
      shareTitle: 'AI智能体使用邀请',
      shareDescription: '注册即可获得额外免费使用次数，快来体验智能AI助手！',
      shareImageUrl: '',
      posterConfig: {
        backgroundImage: '',
        qrCodeSize: 202,
        qrCodePosition: { x: 0, y: 0 }
      }
    })
    message.success('分享配置已重置')
  } catch (error) {
    console.error('重置配置失败:', error)
    message.error('重置配置失败: ' + (error.message || '未知错误'))
  } finally {
    resetting.value = false
  }
}

const loadConfig = async () => {
  try {
    // 从store中获取数据
    const storeData = referralStore.shareData.value
    if (storeData && storeData.length > 0) {
      // 转换格式
      const configData = {}
      storeData.forEach(item => {
        configData[item.key] = item.value
      })
      
      Object.assign(formData, {
        shareTitle: configData.shareTitle || 'AI智能体使用邀请',
        shareDescription: configData.shareDescription || '注册即可获得额外免费使用次数，快来体验智能AI助手！',
        shareImageUrl: configData.shareImageUrl || '',
        posterConfig: {
          backgroundImage: configData.posterBackgroundImage || '',
          qrCodeSize: parseInt(configData.posterQrCodeSize) || 202,
          qrCodePosition: {
            x: parseInt(configData.posterQrCodePositionX) || 0,
            y: parseInt(configData.posterQrCodePositionY) || 0
          }
        }
      })
    } else {
      // 如果store中没有数据，从API加载
      await referralStore.loadAllConfig()
      
      // 等待加载完成后更新表单
      const configData = {}
      referralStore.shareData.value.forEach(item => {
        configData[item.key] = item.value
      })
      
      Object.assign(formData, {
        shareTitle: configData.shareTitle || 'AI智能体使用邀请',
        shareDescription: configData.shareDescription || '注册即可获得额外免费使用次数，快来体验智能AI助手！',
        shareImageUrl: configData.shareImageUrl || '',
        posterConfig: {
          backgroundImage: configData.posterBackgroundImage || '',
          qrCodeSize: parseInt(configData.posterQrCodeSize) || 202,
          qrCodePosition: {
            x: parseInt(configData.posterQrCodePositionX) || 0,
            y: parseInt(configData.posterQrCodePositionY) || 0
          }
        }
      })
    }
  } catch (error) {
    console.warn('加载分享配置失败:', error)
    message.error('加载配置失败: ' + (error.message || '未知错误'))
  }
}

// 打开媒体选择器
function openMediaSelector() {
  showMediaSelector.value = true
}

// 处理媒体选择
function handleMediaSelected(media) {
  formData.shareImageUrl = media.url
  message.success('图片选择成功')
}

// 打开海报编辑器
function openPosterEditor() {
  showPosterEditor.value = true
}

// 处理海报配置更新
function handlePosterConfigUpdate(newConfig) {
  Object.assign(formData.posterConfig, newConfig)
  message.success('海报配置已更新')
}

onMounted(() => {
  loadConfig()
})

// 对外暴露方法
defineExpose({
  loadConfig
})
</script>

<style scoped>
.share-config {
  padding: 12px;
}

.action-buttons {
  margin-top: 16px;
  display: flex;
  gap: 8px;
}

.upload-container {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.preview-wrapper {
  width: 80px;
  height: 80px;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background-color: #fafafa;
  cursor: pointer;
}

.empty-image {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.select-btn {
  flex-shrink: 0;
}

.header-actions {
  position: absolute;
  top: 20px;
  right: 20px;
}

/* 海报相关样式 */
.poster-container {
  display: flex;
  gap: 16px;
  align-items: flex-start;
}

.poster-preview-card {
  flex-shrink: 0;
}

.poster-preview-image {
  width: 90px;
  height: 150px;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  background-color: #fafafa;
  background-size: cover;
  background-position: center;
  position: relative;
  overflow: hidden;
}

.empty-poster {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #999;
  font-size: 12px;
}

.qr-preview {
  position: absolute;
  background-color: rgba(100, 100, 255, 0.3);
  border: 1px solid #6366f1;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6366f1;
  font-size: 10px;
  font-weight: bold;
}

.poster-actions {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}
</style> 