import 'reflect-metadata';
import express, { Request, Response, NextFunction } from 'express';
import cors from 'cors';
import { config } from 'dotenv';
import { AppDataSource } from './data-source';
import configNewRoutes from './routes/config-new.routes';
import redemptionCodeRoutes from './routes/redemption-code.routes';
import packageRoutes from './routes/package.routes';
import agentPackageRoutes from './routes/agent-package.routes';
import userRoutes from './routes/user.routes';
import authRoutes from './routes/auth.routes';
import agentGroupRoutes from './routes/agent-group.routes';
import agentThemeRoutes from './routes/agent-theme.routes';
import agentOrderRoutes from './routes/agent-order.routes';
import taskRoutes from './routes/task.routes';
import cozeProxyRoutes from './routes/coze-proxy.routes'; // 导入扣子API代理路由
import cozeUploadRoutes from './routes/coze-upload.routes'; // 导入扣子文件上传路由
import paymentRoutes from './routes/payment.routes'; // 导入支付路由
import dailyLimitRoutes from './routes/daily-limit.routes'; // 导入每日限制路由
import fs from 'fs';
import path from 'path';
import net from 'net';
import { createDatabase } from './utils/database';
import mediaRoutes from './routes/media';
import bannerRoutes from './routes/banner.routes';
import { Routes } from './routes';  // 导入Routes
import multer from 'multer';
import { Config } from './entity/Config';
import { authMiddleware, adminMiddleware } from './middleware/auth.middleware';
import * as authController from './controllers/auth.controller';
import * as morgan from 'morgan';
import { Task } from './entity/Task';
import { createServer } from 'http';
import { loadEnv } from './env-loader';
import configRoutes from './routes/configRoutes';
import verificationRoutes from './routes/verificationRoutes';
import { Member } from './entity/Member';
import * as mysql from 'mysql2/promise';
import * as bcrypt from 'bcrypt';

// 加载环境变量（使用增强的环境变量加载器）
loadEnv();

// 如果.env文件不存在，创建一个默认的.env文件
const envPath = path.join(__dirname, '../.env');
if (!fs.existsSync(envPath)) {
  const defaultEnv = `PORT=3030
NODE_ENV=development
JWT_SECRET=your_jwt_secret_key_here
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=root
DB_DATABASE=ai_agent
LOG_LEVEL=info
API_DOMAIN=`;
  
  try {
    fs.writeFileSync(envPath, defaultEnv);
    console.log('.env文件已创建');
  } catch (err) {
    console.error('创建.env文件失败:', err);
  }
}

// 服务器状态文件路径（位于项目根目录）
const SERVER_STATUS_FILE = path.join(__dirname, '../../../server-status.json');

// 创建Express应用
const app = express();
// 默认端口及备选端口
const DEFAULT_PORT = 3030;
const ALTERNATIVE_PORTS = [3000, 3001, 8080, 8081, 8082];

// 全局端口变量
let PORT = DEFAULT_PORT;

// 检查端口是否可用
function isPortAvailable(port: number): Promise<boolean> {
  return new Promise((resolve) => {
    const server = net.createServer();
    server.once('error', () => {
      resolve(false);
    });
    server.once('listening', () => {
      server.close();
      resolve(true);
    });
    server.listen(port);
  });
}

// 中间件
app.use(cors({
  origin: '*', // 允许所有来源访问
  methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
  credentials: true,
  optionsSuccessStatus: 204
}));

// 添加中间件设置响应头确保中文正确显示
app.use((req: Request, res: Response, next: NextFunction) => {
  res.setHeader('Content-Type', 'application/json; charset=utf-8');
  next();
});

// 添加错误处理中间件
app.use((error: any, req: Request, res: Response, next: NextFunction) => {
  console.error('全局错误处理:', error);
  
  // 避免重复发送响应
  if (res.headersSent) {
    return next(error);
  }
  
  // 安全地返回错误信息
  try {
    const errorMessage = error.message || '服务器内部错误';
    const errorDetails = process.env.NODE_ENV === 'development' 
      ? (typeof error.stack === 'string' ? error.stack : JSON.stringify(error)) 
      : {};
    
    res.status(500).json({ 
      message: errorMessage, 
      error: errorDetails 
    });
  } catch (sendError) {
    console.error('发送错误响应失败:', sendError);
    res.status(500).send('服务器内部错误');
  }
});

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 静态文件服务
const publicPath = path.join(__dirname, '../../../public');
app.use(express.static(publicPath)); // 直接提供public目录的静态文件服务
console.log(`提供静态文件服务: ${publicPath}`);

// 专门的图片路由（用于调试）
app.get('/images/:filename', (req: Request, res: Response) => {
  const filename = req.params.filename;
  const imagePath = path.join(publicPath, 'images', filename);

  console.log('请求图片:', {
    filename,
    imagePath,
    exists: fs.existsSync(imagePath)
  });

  if (fs.existsSync(imagePath)) {
    res.sendFile(imagePath);
  } else {
    res.status(404).json({ error: '图片不存在' });
  }
});

// 确保uploads目录存在
const uploadsPath = path.join(publicPath, 'images');
if (!fs.existsSync(uploadsPath)) {
  fs.mkdirSync(uploadsPath, { recursive: true });
  console.log(`创建图片上传目录: ${uploadsPath}`);
}

// 创建视频目录（在public下）
const videosPath = path.join(publicPath, 'videos');
if (!fs.existsSync(videosPath)) {
  fs.mkdirSync(videosPath, { recursive: true });
  console.log(`创建视频上传目录: ${videosPath}`);
}
console.log('媒体上传目录已创建');

// 配置微信验证文件上传
const wechatVerifyStorage = multer.diskStorage({
  destination: function (req, file, cb) {
    // 将验证文件保存在public目录，这样可以直接通过HTTP访问
    const publicPath = path.join(__dirname, '../../../public');
    // 确保目录存在
    if (!fs.existsSync(publicPath)) {
      fs.mkdirSync(publicPath, { recursive: true });
    }
    console.log('保存验证文件到目录:', publicPath);
    cb(null, publicPath);
  },
  filename: function (req, file, cb) {
    // 保持原始文件名
    console.log('处理文件名:', file.originalname);
    cb(null, file.originalname);
  }
});

const uploadWechatVerify = multer({
  storage: wechatVerifyStorage,
  fileFilter: (req, file, cb) => {
    console.log('文件上传请求(fileFilter):', {
      originalname: file.originalname,
      mimetype: file.mimetype,
      fieldname: file.fieldname
    });
    
    // 只接受txt文件，但放宽限制
    if (!file.originalname.toLowerCase().endsWith('.txt') && 
        !file.mimetype.includes('text/plain')) {
      console.log('文件类型验证失败: 不是txt文件');
      return cb(new Error('只允许上传txt文件'));
    }
    
    // 放宽文件名验证，不再强制要求MP_verify_前缀
    cb(null, true);
  },
  limits: {
    fileSize: 20480 // 限制文件大小20KB
  }
});

// 头像上传配置
const avatarStorage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, '../../../public/images');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const timestamp = Date.now();
    const randomStr = Math.random().toString(36).substring(2, 8);
    const ext = path.extname(file.originalname);
    const filename = `avatar_${timestamp}_${randomStr}${ext}`;
    cb(null, filename);
  }
});

const avatarUpload = multer({
  storage: avatarStorage,
  fileFilter: (req, file, cb) => {
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.mimetype)) {
      return cb(new Error('只允许上传图片文件'));
    }
    cb(null, true);
  },
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB
  }
});

// 头像上传API
app.post('/api/members/upload-avatar', authMiddleware, avatarUpload.single('avatar'), async (req: Request, res: Response) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '未找到上传文件'
      });
    }

    // 只保存文件名到数据库，不保存完整URL
    const filename = req.file.filename;

    // 生成完整的访问URL（返回给前端）
    const baseUrl = `http://localhost:${PORT}`;
    const avatarUrl = `${baseUrl}/images/${filename}`;

    console.log('头像上传成功:', {
      userId: (req as any).user.id,
      originalName: req.file.originalname,
      filename: filename,
      size: req.file.size,
      avatarUrl: avatarUrl
    });

    // 更新数据库中的头像字段（只保存文件名）
    try {
      const memberRepository = AppDataSource.getRepository(Member);
      const updateResult = await memberRepository.update((req as any).user.id, { avatar: filename });
      console.log('数据库更新结果:', updateResult);

      // 验证更新是否成功
      if (updateResult.affected === 0) {
        console.warn('警告：没有记录被更新，用户ID可能不存在:', (req as any).user.id);
      } else {
        console.log('头像字段更新成功，用户ID:', (req as any).user.id, '文件名:', filename);
      }
    } catch (dbError) {
      console.error('数据库更新失败:', dbError);
      // 即使数据库更新失败，文件已经上传成功，所以仍然返回成功
      // 但是记录错误日志
    }

    return res.json({
      success: true,
      message: '头像上传成功',
      data: {
        avatarUrl: avatarUrl,
        filename: req.file.filename,
        originalName: req.file.originalname,
        size: req.file.size
      }
    });

  } catch (error) {
    console.error('头像上传失败:', error);
    return res.status(500).json({
      success: false,
      message: '头像上传失败',
      error: (error as Error).message
    });
  }
});

// 微信验证文件上传路由
app.post('/api/upload/wechat-verify', adminMiddleware, (req: Request, res: Response, next: NextFunction) => {
  console.log('接收到原始上传请求:', {
    headers: req.headers,
    contentType: req.headers['content-type'],
    hasBody: !!req.body,
    bodyKeys: req.body ? Object.keys(req.body) : []
  });
  
  // 使用multer中间件处理文件上传
  uploadWechatVerify.single('file')(req, res, async (err) => {
    if (err) {
      console.error('Multer处理文件失败:', err);
      return res.status(400).json({
        code: 400,
        message: err.message || '文件上传失败'
      });
    }
    
    console.log('Multer处理后的请求:', {
      body: req.body,
      file: req.file ? {
        fieldname: req.file.fieldname,
        originalname: req.file.originalname,
        mimetype: req.file.mimetype,
        size: req.file.size,
        path: req.file.path
      } : '无文件'
    });

    // 如果没有文件被上传
    if (!req.file) {
      return res.status(400).json({
        code: 400,
        message: '未找到上传文件'
      });
    }

    try {
      // 读取文件内容
      const fileContent = fs.readFileSync(req.file.path, 'utf8');

      // 确定配置类型（公众号或小程序）
      const configType = req.body.type || 'wechat-official';
      const isMiniprogramFile = configType === 'wechat-miniprogram';
      
      console.log('验证文件上传成功:', {
        type: isMiniprogramFile ? '微信小程序' : '微信公众号',
        filename: req.file.originalname,
        originalname: req.file.originalname,
        path: req.file.path,
        size: req.file.size,
        content: fileContent
      });
      
      const verifyFileData = {
        filename: req.file.originalname,
        content: fileContent.trim()
      };

      // 根据类型选择对应的配置表
      if (isMiniprogramFile) {
        // 微信小程序配置
        const { ConfigWechatMiniprogram } = require('./entity/ConfigWechatMiniprogram');
        const configRepository = AppDataSource.getRepository(ConfigWechatMiniprogram);
        let config = await configRepository.findOne({ where: { id: 1 } });

        if (config) {
          config.verifyFileName = verifyFileData.filename;
          config.verifyFileContent = verifyFileData.content;
          await configRepository.save(config);
          console.log('更新了微信小程序验证文件配置');
        } else {
          // 创建新配置
          const newConfig = configRepository.create({
            enabled: false,
            appId: '',
            appSecret: '',
            verifyFileName: verifyFileData.filename,
            verifyFileContent: verifyFileData.content
          });
          await configRepository.save(newConfig);
          console.log('创建了微信小程序验证文件配置');
        }
      } else {
        // 微信公众号配置
        const { ConfigWechatOfficial } = require('./entity/ConfigWechatOfficial');
        const configRepository = AppDataSource.getRepository(ConfigWechatOfficial);
        let config = await configRepository.findOne({ where: { id: 1 } });

        if (config) {
          config.verifyFileName = verifyFileData.filename;
          config.verifyFileContent = verifyFileData.content;
          await configRepository.save(config);
          console.log('更新了微信公众号验证文件配置');
        } else {
          // 创建新配置
          const newConfig = configRepository.create({
            enabled: true, // 公众号配置始终启用
            appId: '',
            appSecret: '',
            token: '',
            encodingAesKey: '',
            verifyFileName: verifyFileData.filename,
            verifyFileContent: verifyFileData.content
          });
          await configRepository.save(newConfig);
          console.log('创建了微信公众号验证文件配置');
        }
      }

      return res.json({
        code: 200,
        message: '文件上传成功',
        data: verifyFileData
      });
    } catch (error: any) {
      console.error('上传微信验证文件失败:', error);
      return res.status(500).json({
        code: 500,
        message: '文件上传失败',
        error: error.message
      });
    }
  });
});

// 前端兼容路由 - 必须在其他配置路由之前
import { ConfigController } from './controller/config.controller';
const frontendConfigController = new ConfigController();

// 配置状态和测试路由 - 这些路由需要在通用路由之前
app.get('/api/configs/status/all', authMiddleware, frontendConfigController.getAllConfigStatus.bind(frontendConfigController));
app.post('/api/configs/:configType/test', adminMiddleware, frontendConfigController.testConfig.bind(frontendConfigController));

// 添加扣子API代理路由（不需要身份验证）
app.use('/api/proxy', cozeProxyRoutes);
console.log('已加载扣子API代理路由');

// 新的配置路由 - 使用新的独立配置表
app.use('/api/configs', configNewRoutes);

// 前端兼容的配置路由 - 作为后备方案，放在新路由之后
app.get('/api/configs/:configType', authMiddleware, frontendConfigController.getFrontendConfigByType.bind(frontendConfigController));
app.post('/api/configs/:configType', adminMiddleware, frontendConfigController.saveFrontendConfigByType.bind(frontendConfigController));
app.use('/api/redemption-codes', redemptionCodeRoutes);
app.use('/api/packages', packageRoutes);
app.use('/api/agent-packages', agentPackageRoutes);
app.use('/api/users', userRoutes);
app.use('/api/auth', authRoutes);
app.use('/api/agent-groups', agentGroupRoutes);
app.use('/api/agent-themes', agentThemeRoutes);
app.use('/api/agent-orders', agentOrderRoutes);
app.use('/api/media', mediaRoutes);
app.use('/api/upload', cozeUploadRoutes); // 添加扣子文件上传路由
app.use('/api/banners', bannerRoutes);
app.use('/api/tasks', taskRoutes);
app.use('/api/payment', paymentRoutes); // 添加支付路由
app.use('/api/daily-limit', dailyLimitRoutes); // 添加每日限制路由

// 添加/api/users/me端点，重定向到/api/auth/me
app.get('/api/users/me', (req, res, next) => {
  req.url = '/api/auth/me';
  app._router.handle(req, res, next);
});

// 添加/api/auth/me端点，获取当前用户信息
app.get('/api/auth/me', authMiddleware, authController.getCurrentUser);

// 添加明确的/api/members/me端点，确保使用会员控制器
import { MemberController } from './controller/MemberController';
const memberController = new MemberController();
app.get('/api/members/me', authMiddleware, memberController.getCurrentMember.bind(memberController));

// 添加会员更新信息端点
app.put('/api/members/me', authMiddleware, async (req: Request, res: Response) => {
  try {
    // 从认证中间件获取用户ID
    const userId = (req as any).user?.id;
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '未授权的访问'
      });
    }

    // 调用会员控制器的更新方法
    req.params.id = userId.toString();
    return await memberController.updateProfile(req, res);
  } catch (error) {
    console.error('更新会员信息失败:', error);
    return res.status(500).json({
      success: false,
      message: '更新会员信息失败'
    });
  }
});

// 添加会员修改密码端点
app.post('/api/members/change-password', authMiddleware, async (req: Request, res: Response) => {
  try {
    const userId = (req as any).user?.id;
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '未授权的访问'
      });
    }

    const { currentPassword, newPassword, confirmPassword } = req.body;

    // 验证必需字段
    if (!newPassword || !confirmPassword) {
      return res.status(400).json({
        success: false,
        message: '请填写新密码和确认密码'
      });
    }

    if (newPassword !== confirmPassword) {
      return res.status(400).json({
        success: false,
        message: '新密码和确认密码不匹配'
      });
    }

    // 获取数据库连接
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '3306'),
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'root',
      database: process.env.DB_NAME || 'ai_agent'
    });

    try {
      // 查找用户
      const [members] = await connection.query(
        'SELECT id, phone, email, password FROM members WHERE id = ?',
        [userId]
      );

      if (!Array.isArray(members) || members.length === 0) {
        await connection.end();
        return res.status(404).json({
          success: false,
          message: '用户不存在'
        });
      }

      const member = members[0] as any;

      // 如果用户已设置密码，需要验证当前密码
      // 检查password字段是否有值（不为NULL且不为空字符串）
      const hasExistingPassword = member.password && member.password.trim() !== '';

      if (hasExistingPassword) {
        if (!currentPassword) {
          await connection.end();
          return res.status(400).json({
            success: false,
            message: '请输入当前密码'
          });
        }

        // 验证当前密码
        const isPasswordValid = await bcrypt.compare(currentPassword, member.password);
        if (!isPasswordValid) {
          await connection.end();
          return res.status(400).json({
            success: false,
            message: '当前密码不正确'
          });
        }
      }

      // 加密新密码
      const hashedPassword = await bcrypt.hash(newPassword, 10);

      // 更新密码
      await connection.query(
        'UPDATE members SET password = ? WHERE id = ?',
        [hashedPassword, userId]
      );

      await connection.end();

      return res.json({
        success: true,
        message: hasExistingPassword ? '密码修改成功' : '密码设置成功'
      });

    } catch (dbError) {
      await connection.end();
      throw dbError;
    }

  } catch (error) {
    console.error('修改密码失败:', error);
    return res.status(500).json({
      success: false,
      message: '修改密码失败'
    });
  }
});

// 添加/api/users/change-password端点，重定向到/api/auth/change-password
app.post('/api/users/change-password', (req, res, next) => {
  req.url = '/api/auth/change-password';
  app._router.handle(req, res, next);
});

// 处理PUT请求更新用户信息
app.put('/api/users/me', authMiddleware, (req, res, next) => {
  // 这里不使用重定向，因为我们已经在userRoutes中添加了直接处理用户更新信息的路由
  next();
});

// 不需要认证的路由列表
const publicRoutes = [
    '/api/members/register',
    '/api/members/login',
    '/api/members/set-test-password',
    '/api/members/:id',  // 添加获取会员详情接口为公开路由
    '/api/auth/login',
    '/api/auth/register',
    '/api/verification/send',  // 发送验证码
    '/api/verification/verify'  // 验证验证码
];

// 注册所有路由
Routes.forEach(route => {
    // 检查是否为公开路由（支持精确匹配和模式匹配）
    const isPublicRoute = publicRoutes.includes(route.route) ||
                         (route.route === '/api/members/:id' && route.method === 'get');

    console.log(`注册路由: ${route.method.toUpperCase()} ${route.route} - 公开路由: ${isPublicRoute}`);

    if (isPublicRoute) {
        // 公开路由，不需要认证
        (app as any)[route.method](
            route.route,
            async (req: Request, res: Response, next: NextFunction) => {
                try {
                    // 通过控制器处理请求
                    const controller = new (route.controller as any)();
                    const method = route.action;

                    // 检查方法是否存在
                    if (typeof controller[method] !== 'function') {
                        console.error(`Method ${method} not found on controller ${route.controller.name}`);
                        return res.status(500).json({ message: `Controller method ${method} not found` });
                    }

                    // 调用控制器方法
                    const result = await controller[method](req, res, next);

                    // 如果响应已经发送，直接返回
                    if (res.headersSent) {
                        return;
                    }

                    // 如果有返回值且响应未发送，发送响应
                    if (result !== undefined && result !== null) {
                        return res.json(result);
                    }
                } catch (error) {
                    console.error(`Error in route ${route.method} ${route.route}:`, error);
                    next(error);
                }
            }
        );
    } else {
        // 需要认证的路由
        (app as any)[route.method](
            route.route,
            authMiddleware,  // 身份验证中间件
            async (req: Request, res: Response, next: NextFunction) => {
                try {
                    // 通过控制器处理请求
                    const controller = new (route.controller as any)();
                    const method = route.action;

                    // 检查方法是否存在
                    if (typeof controller[method] !== 'function') {
                        console.error(`Method ${method} not found on controller ${route.controller.name}`);
                        return res.status(500).json({ message: `Controller method ${method} not found` });
                    }

                    // 调用控制器方法
                    const result = await controller[method](req, res, next);

                    // 如果响应已经发送，直接返回
                    if (res.headersSent) {
                        return;
                    }

                    // 如果有返回值且响应未发送，发送响应
                    if (result !== undefined && result !== null) {
                        return res.json(result);
                    }
                } catch (error) {
                    console.error(`Error in route ${route.method} ${route.route}:`, error);
                    next(error);
                }
            }
        );
    }
});

// 同时注册admin路由（用于前端访问）
app.use('/admin/redemption-codes', redemptionCodeRoutes);
app.use('/admin/packages', packageRoutes);
app.use('/admin/agent-packages', agentPackageRoutes);
app.use('/admin/users', userRoutes);
app.use('/admin/agent-groups', agentGroupRoutes);
app.use('/admin/agent-themes', agentThemeRoutes);
app.use('/admin/agent-orders', agentOrderRoutes);
app.use('/admin/media', mediaRoutes);
app.use('/admin/banners', bannerRoutes);
app.use('/admin/tasks', taskRoutes);

// 注册新的配置路由
app.use('/api/config', configRoutes);

// 注册公开的推广相关路由（不需要认证）- 放在通用路由之后避免冲突
import * as configController from './controllers/config.controller';
// 这些路由已经在 configRoutes 中定义，这里注释掉避免重复
// app.get('/api/config/referral', configController.getReferralConfig);
// app.post('/api/config/generate-invite-code', configController.generateInviteCode);
// app.post('/api/config/save-poster', configController.savePoster);

// 注册验证码路由（不需要身份验证）
app.use('/api/verification', verificationRoutes);

// 添加健康检查接口
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'ok',
    timestamp: new Date().toISOString()
  });
});

// 添加公开的会员详情接口（不需要认证）
app.get('/api/members/:id/public', async (req, res) => {
  try {
    const mysql = require('mysql2/promise');
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '123456',
      database: 'ai_agent_admin'
    });

    const id = parseInt(req.params.id);
    const [rows] = await connection.execute(
      'SELECT * FROM members WHERE id = ?',
      [id]
    );

    await connection.end();

    if (rows.length === 0) {
      return res.status(404).json({ message: '会员不存在' });
    }

    const member = rows[0];

    // 返回会员信息（不包含敏感信息）
    res.json({
      id: member.id,
      name: member.name,
      phone: member.phone,
      email: member.email,
      avatar: member.avatar || '',
      balance: member.balance || 0,
      points: member.points || 0,
      status: member.status,
      createdAt: member.createdAt,
      lastLoginTime: member.lastLoginTime
    });

  } catch (error) {
    console.error('获取会员信息失败:', error);
    res.status(500).json({ message: '获取会员信息失败' });
  }
});

// 添加备用登录接口（使用mysql2，避免TypeORM问题）
app.post('/api/members/login-backup', async (req, res) => {
  try {
    const mysql = require('mysql2/promise');
    const bcrypt = require('bcrypt');

    const { username, password } = req.body;

    if (!username || !password) {
      return res.status(400).json({
        success: false,
        message: '用户名和密码不能为空'
      });
    }

    // 数据库连接配置
    const dbConfig = {
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '3306'),
      user: process.env.DB_USERNAME || 'root',
      password: process.env.DB_PASSWORD || 'root',
      database: process.env.DB_DATABASE || 'ai_agent'
    };

    // 连接数据库
    const connection = await mysql.createConnection(dbConfig);

    // 查找用户
    const [members] = await connection.query(
      'SELECT * FROM members WHERE phone = ?',
      [username]
    );

    if (members.length === 0) {
      await connection.end();
      return res.status(401).json({
        success: false,
        message: '用户不存在'
      });
    }

    const member = members[0];
    console.log('找到用户:', { id: member.id, phone: member.phone, hasPassword: !!member.password });

    // 验证密码
    if (!member.password) {
      await connection.end();
      return res.status(401).json({
        success: false,
        message: '用户密码未设置'
      });
    }

    // 尝试验证密码（支持明文和加密密码）
    let isPasswordValid = false;

    // 先尝试明文密码比较
    if (password === member.password) {
      isPasswordValid = true;
      console.log('明文密码验证成功');
    } else {
      // 再尝试加密密码比较
      try {
        isPasswordValid = await bcrypt.compare(password, member.password);
        console.log('加密密码验证结果:', isPasswordValid);
      } catch (error: any) {
        console.log('bcrypt验证失败，可能是明文密码:', error.message);
        isPasswordValid = false;
      }
    }

    if (!isPasswordValid) {
      await connection.end();
      return res.status(401).json({
        success: false,
        message: '密码错误'
      });
    }

    await connection.end();

    // 登录成功
    res.json({
      success: true,
      message: '登录成功',
      data: {
        token: 'temp_token_' + Date.now(),
        id: member.id,
        username: member.phone || member.email,
        nickname: member.name || member.phone,
        avatar: member.avatar || '',
        phone: member.phone || '',
        email: member.email || '',
        balance: member.balance || 0,
        points: member.points || 0,
        status: member.status ? 'active' : 'inactive',
        registrationTime: member.createdAt,
        lastLoginTime: member.lastLoginTime
      }
    });

  } catch (error) {
    console.error('登录失败:', error);
    res.status(500).json({
      success: false,
      message: '登录失败'
    });
  }
});

// 获取当前用户信息（备用API，不需要认证）
app.get('/api/user-info-backup', async (req, res) => {
  try {
    // 这里应该从token中获取用户ID，但为了简化，我们使用查询参数
    const userId = req.query.userId || req.headers['user-id'];

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '未提供用户ID'
      });
    }

    const mysql = require('mysql2/promise');
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '123456',
      database: 'ai_agent_admin'
    });

    const [rows] = await connection.execute(
      'SELECT * FROM members WHERE id = ?',
      [userId]
    );

    await connection.end();

    if (rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    const member = rows[0];

    // 处理头像URL：如果数据库中只有文件名，则拼接完整URL
    let avatarUrl = '';
    if (member.avatar) {
      if (member.avatar.startsWith('http')) {
        // 如果已经是完整URL，直接使用
        avatarUrl = member.avatar;
      } else {
        // 如果只是文件名，拼接完整URL
        avatarUrl = `http://localhost:${PORT}/images/${member.avatar}`;
      }
    }

    // 返回用户信息（不包含密码）
    res.json({
      success: true,
      message: '获取用户信息成功',
      data: {
        id: member.id,
        username: member.phone || member.email,
        nickname: member.name || member.phone,
        avatar: avatarUrl,
        phone: member.phone || '',
        email: member.email || '',
        balance: member.balance || 0,
        points: member.points || 0,
        status: member.status ? 'active' : 'inactive',
        registrationTime: member.createdAt,
        lastLoginTime: member.lastLoginTime
      }
    });

  } catch (error) {
    console.error('获取用户信息失败:', error);
    res.status(500).json({
      success: false,
      message: '获取用户信息失败，请稍后重试'
    });
  }
});

// 添加备用任务API（使用mysql2，避免TypeORM问题）
app.get('/api/tasks-backup', async (req, res) => {
  try {
    const mysql = require('mysql2/promise');

    const { userId, page = 1, pageSize = 20, type, status } = req.query;

    console.log('任务API请求参数:', { userId, page, pageSize, type, status });

    // 数据库连接配置
    const dbConfig = {
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '3306'),
      user: process.env.DB_USERNAME || 'root',
      password: process.env.DB_PASSWORD || 'root',
      database: process.env.DB_DATABASE || 'ai_agent'
    };

    // 连接数据库
    const connection = await mysql.createConnection(dbConfig);

    // 构建查询条件
    let whereClause = '';
    let queryParams: any[] = [];
    const conditions: string[] = [];

    // 用户ID筛选（必须）
    if (userId) {
      conditions.push('userId = ?');
      queryParams.push(userId);
    }

    // 任务类型筛选
    if (type && type !== 'all') {
      const taskType = type === 'agent' ? '智能体' : '工作流';
      conditions.push('taskType = ?');
      queryParams.push(taskType);
    }

    // 状态筛选
    if (status && status !== 'all') {
      conditions.push('status = ?');
      queryParams.push(status);
    }

    if (conditions.length > 0) {
      whereClause = 'WHERE ' + conditions.join(' AND ');
    }

    // 分页计算
    const pageNum = parseInt(String(page));
    const pageSizeNum = parseInt(String(pageSize));
    const offset = (pageNum - 1) * pageSizeNum;

    // 查询任务总数
    const countQuery = `SELECT COUNT(*) as total FROM task ${whereClause}`;
    const [countResult] = await connection.query(countQuery, queryParams);
    const total = (countResult as any)[0].total;

    // 查询任务列表
    const tasksQuery = `
      SELECT * FROM task
      ${whereClause}
      ORDER BY id DESC
      LIMIT ? OFFSET ?
    `;
    const [tasks] = await connection.query(tasksQuery, [...queryParams, pageSizeNum, offset]);

    await connection.end();

    // 直接使用查询结果，不添加模拟数据
    let finalTasks = tasks as any[];

    // 返回结果
    const finalTotal = finalTasks.length;
    res.json({
      code: 200,
      message: '获取任务列表成功',
      data: {
        tasks: finalTasks,
        pagination: {
          total: finalTotal,
          page: pageNum,
          pageSize: pageSizeNum
        },
        stats: {
          totalTasks: finalTotal,
          completedTasks: finalTasks.filter((task: any) => task.status === '已完成').length,
          weeklyTokens: finalTasks.reduce((sum: number, task: any) => sum + ((task.consumption?.tokens) || 0), 0),
          totalTokens: finalTasks.reduce((sum: number, task: any) => sum + ((task.consumption?.tokens) || 0), 0),
          remainingTokens: 10000 - finalTasks.reduce((sum: number, task: any) => sum + ((task.consumption?.tokens) || 0), 0)
        }
      }
    });

  } catch (error) {
    console.error('获取任务列表失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取任务列表失败'
    });
  }
});

// 添加创建测试任务的接口
app.post('/api/create-test-tasks', async (req, res) => {
  try {
    const mysql = require('mysql2/promise');
    const { userId } = req.body;

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: '用户ID不能为空'
      });
    }

    // 数据库连接配置
    const dbConfig = {
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '3306'),
      user: process.env.DB_USERNAME || 'root',
      password: process.env.DB_PASSWORD || 'root',
      database: process.env.DB_DATABASE || 'ai_agent'
    };

    // 连接数据库
    const connection = await mysql.createConnection(dbConfig);

    // 创建测试任务数据
    const testTasks = [
      {
        userId: userId,
        userName: '测试用户',
        taskSource: 'WEB',
        taskType: '智能体',
        taskTypeDetail: '文案助手',
        status: '已完成',
        input: '帮我写一篇关于人工智能的文章',
        output: '人工智能是当今科技发展的重要方向，它正在改变我们的生活和工作方式...',
        consumption: JSON.stringify({ tokens: 150, inputToken: 50, outputToken: 100 }),
        logs: '任务执行成功',
        createTime: new Date(),
        updateTime: new Date()
      },
      {
        userId: userId,
        userName: '测试用户',
        taskSource: 'WEB',
        taskType: '智能体',
        taskTypeDetail: '代码助手',
        status: '已完成',
        input: '帮我写一个Python排序函数',
        output: 'def bubble_sort(arr):\n    for i in range(len(arr)):\n        for j in range(0, len(arr)-i-1):\n            if arr[j] > arr[j+1]:\n                arr[j], arr[j+1] = arr[j+1], arr[j]\n    return arr',
        consumption: JSON.stringify({ tokens: 200, inputToken: 80, outputToken: 120 }),
        logs: '任务执行成功',
        createTime: new Date(),
        updateTime: new Date()
      },
      {
        userId: userId,
        userName: '测试用户',
        taskSource: 'WEB',
        taskType: '工作流',
        taskTypeDetail: '数据处理',
        status: '执行中',
        input: '处理用户数据文件',
        output: '',
        consumption: JSON.stringify({ tokens: 0, inputToken: 0, outputToken: 0 }),
        logs: '正在处理中...',
        createTime: new Date(),
        updateTime: new Date()
      }
    ];

    // 插入测试任务
    for (const task of testTasks) {
      await connection.query(
        `INSERT INTO task (userId, userName, taskSource, taskType, taskTypeDetail, status, input, output, consumption, logs, createTime, updateTime)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [task.userId, task.userName, task.taskSource, task.taskType, task.taskTypeDetail, task.status, task.input, task.output, task.consumption, task.logs, task.createTime, task.updateTime]
      );
    }

    await connection.end();

    res.json({
      success: true,
      message: `成功为用户${userId}创建${testTasks.length}个测试任务`
    });

  } catch (error) {
    console.error('创建测试任务失败:', error);
    res.status(500).json({
      success: false,
      message: '创建测试任务失败'
    });
  }
});

// 添加ping端点用于前端检测服务可用性
app.get('/api/ping', (req, res) => {
  res.status(200).json({
    status: 'ok',
    port: req.socket.localPort,
    timestamp: new Date().toISOString()
  });
});

// 写入服务器状态文件
function writeServerStatusFile(port: number) {
  try {
    // 使用API_DOMAIN（如果存在）或回退到localhost
    const domain = process.env.API_DOMAIN || `http://localhost:${port}`;
    const apiBase = domain.endsWith('/api') ? domain : `${domain}/api`;
    
    const data = {
      url: domain,
      port,
      api: apiBase,
      status: 'online',
      timestamp: new Date().toISOString()
    };
    fs.writeFileSync(SERVER_STATUS_FILE, JSON.stringify(data, null, 2));
    console.log(`服务器状态已写入: ${SERVER_STATUS_FILE}`);
    console.log(`API域名: ${domain}`);
  } catch (err) {
    console.error('写入服务器状态文件失败:', err);
  }
}

// 查找可用端口并启动服务器
async function startServer() {
  let port = DEFAULT_PORT;
  
  // 检查默认端口是否可用
  if (!await isPortAvailable(DEFAULT_PORT)) {
    console.log(`默认端口 ${DEFAULT_PORT} 已被占用，尝试备用端口...`);
    
    // 尝试备选端口
    for (const alternativePort of ALTERNATIVE_PORTS) {
      if (await isPortAvailable(alternativePort)) {
        port = alternativePort;
        console.log(`找到可用端口: ${port}`);
        break;
      }
    }

    if (port === DEFAULT_PORT) {
      console.error('无法找到可用的端口，服务器启动失败');
      process.exit(1);
    }
  }

  // 更新全局PORT变量
  PORT = port;

  // 启动服务器
  app.listen(port, async () => {
    console.log(`服务器运行在: http://localhost:${port}`);
    console.log(`API端点: http://localhost:${port}/api`);
    console.log(`健康检查: http://localhost:${port}/health`);

    // 写入服务器状态文件，供前端读取
    writeServerStatusFile(port);

    // 启动套餐到期监控服务
    try {
      const { PackageExpiryService } = await import('./services/package-expiry.service');
      PackageExpiryService.startScheduledTask();
      console.log('套餐到期监控服务已启动');
    } catch (error) {
      console.error('启动套餐到期监控服务失败:', error);
    }

    // 启动消耗点数监控服务
    try {
      const { consumptionMonitor } = await import('./scripts/monitor-consumption-reset');
      // 创建初始备份
      await consumptionMonitor.createBackup();
      // 启动定期监控（每30分钟检查一次）
      consumptionMonitor.startPeriodicMonitoring(30);
      console.log('消耗点数监控服务已启动');
    } catch (error) {
      console.error('启动消耗点数监控服务失败:', error);
    }
  });
}

// 检查任务表是否存在
async function checkTaskTable() {
    try {
        const taskRepository = AppDataSource.getRepository(Task);
        const taskCount = await taskRepository.count();
        console.log(`task表是否存在: ${taskCount >= 0 ? '是' : '否'}`);

        if (taskCount === 0) {
            // 跳过初始化测试数据，避免阻塞启动
            console.log('任务表为空，跳过初始化测试数据以避免阻塞启动');
        }

        console.log('任务表检查完成');
    } catch (error) {
        console.error('检查任务表失败:', error);
    }
}

// 尝试导入日志工具
let logger: any;
try {
  const loggerPath = path.resolve(__dirname, '../../utils/logger');
  logger = require(loggerPath);
} catch (err) {
  // 如果找不到日志工具，使用默认控制台
  console.log("无法加载日志工具，使用默认日志输出");
  logger = {
    server: console.log,
    error: console.error,
    info: console.log,
    serverStarted: () => {}
  };
}

// 启动服务器
const httpServer = createServer(app);

// 初始化数据库连接
AppDataSource.initialize()
  .then(async () => {
    logger.info("数据库连接成功");
    
    // 添加数据库连接错误处理
    try {
      // @ts-ignore - 兼容TypeORM内部实现
      const mysqlConnection = AppDataSource.manager.connection.driver.pool;
      if (mysqlConnection && typeof mysqlConnection.on === 'function') {
        mysqlConnection.on('error', (err: Error) => {
          console.error('数据库连接错误，尝试重新连接:', err);
          setTimeout(() => {
            console.log('尝试重新初始化数据库连接...');
            AppDataSource.initialize().catch((initErr: Error) => {
              console.error('重新连接数据库失败:', initErr);
            });
          }, 5000);
        });
      }
    } catch (error) {
      console.warn('无法添加数据库连接监控:', error);
    }
    
    // 添加定期检查连接
    setInterval(async () => {
      try {
        // 简单查询测试连接
        await AppDataSource.query('SELECT 1');
      } catch (error) {
        console.error('连接检查失败，尝试重新连接:', error);
        try {
          await AppDataSource.destroy();
          await AppDataSource.initialize();
          console.log('数据库连接已重新初始化');
        } catch (reconnectError) {
          console.error('重新初始化连接失败:', reconnectError);
        }
      }
    }, 60000); // 每分钟检查一次
    
    // 检查任务表
    await checkTaskTable();
    
    // 确保不会自动同步数据库结构，保护现有数据
    AppDataSource.synchronize = async (dropBeforeSync?: boolean) => {
        if (dropBeforeSync) {
            console.log('⚠️ 危险操作被阻止：数据库强制同步被禁用，保护现有数据');
            return Promise.resolve();
        }
        console.log('数据库同步被限制，仅允许安全的表结构更新');
        return Promise.resolve();
    };
    
    // 确保数据库表存在
    try {
        const { initAgentGroupTable, initAgentThemeTable, initAgentOrderTable, initMediaGroupTable, initMediaTable, initAgentPackageTable, initBannerTable, initConfigTables } = await import('./utils/database');
        await initAgentGroupTable();
        await initAgentThemeTable();
        await initAgentOrderTable();
        await initMediaGroupTable();
        await initMediaTable();
        await initAgentPackageTable();
        await initBannerTable();
        await initConfigTables();
        
        // 初始化members表
        const createMembersTable = async () => {
            try {
                // 检查members表是否存在
                const tableExists = await AppDataSource.query(
                    `SELECT EXISTS (
                        SELECT 1 FROM information_schema.tables 
                        WHERE table_schema = DATABASE() 
                        AND table_name = 'members'
                    ) as \`exists\`;`
                );
                
                const exists = tableExists[0].exists;
                console.log('members表是否存在:', exists);
                
                if (!exists) {
                    // 如果表不存在，创建表
                    await AppDataSource.query(`
                        CREATE TABLE members (
                            id SERIAL PRIMARY KEY,
                            name VARCHAR(255),
                            phone VARCHAR(50) UNIQUE NOT NULL,
                            email VARCHAR(255),
                            password VARCHAR(255) NOT NULL,
                            account_type VARCHAR(50) DEFAULT 'account',
                            package_id INT,
                            points INT DEFAULT 0,
                            is_partner BOOLEAN DEFAULT false,
                            referrer_id INT,
                            status BOOLEAN DEFAULT true,
                            avatar VARCHAR(255),
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                        );
                    `);
                    console.log('members表已创建');
                    
                    // 添加示例数据
                    await AppDataSource.query(`
                        INSERT INTO members (name, phone, email, password, account_type, points, is_partner, status)
                        VALUES 
                        ('测试会员1', '***********', '<EMAIL>', '$2b$10$lDJMXLcwCdcjA2nGMqSYUet.wKXCxP0tANIxlRyBkMgH3AlL9CeLa', 'account', 100, false, true),
                        ('测试会员2', '***********', '<EMAIL>', '$2b$10$lDJMXLcwCdcjA2nGMqSYUet.wKXCxP0tANIxlRyBkMgH3AlL9CeLa', 'account', 200, false, true),
                        ('合伙人用户', '***********', '<EMAIL>', '$2b$10$lDJMXLcwCdcjA2nGMqSYUet.wKXCxP0tANIxlRyBkMgH3AlL9CeLa', 'account', 500, true, true);
                    `);
                    console.log('members表示例数据已添加');
                } else {
                    console.log('members表已存在，无需创建');
                }
            } catch (error) {
                console.error('初始化members表失败:', error);
            }
        };
        
        await createMembersTable();

        // 初始化points_records表
        try {
          const { initPointsRecordsTable } = await import('./scripts/init-points-records-table');
          await initPointsRecordsTable();
          console.log('points_records表初始化完成');
        } catch (error) {
          console.error('初始化points_records表失败:', error);
        }

        // 执行关系更新脚本
        const { updateRelations } = await import('./scripts/update-relations');
        await updateRelations();
    } catch (err) {
        console.error('初始化数据库表失败:', err);
    }
    
    // 启动服务器
    await startServer();
  })
  .catch((error) => {
    logger.error(`数据库连接失败: ${error.message}`);
    process.exit(1);
  });

export default app; 