-- =====================================================
-- 配置管理独立表结构设计
-- 将原有的 system_config 表拆分为多个独立的配置表
-- =====================================================

-- 1. 微信公众号配置表
CREATE TABLE IF NOT EXISTS `config_wechat_official` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `enabled` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否启用',
  `app_id` varchar(100) NOT NULL DEFAULT '' COMMENT '微信公众号AppID',
  `app_secret` varchar(255) NOT NULL DEFAULT '' COMMENT '微信公众号AppSecret',
  `token` varchar(100) NOT NULL DEFAULT '' COMMENT '微信公众号Token',
  `encoding_aes_key` varchar(255) NOT NULL DEFAULT '' COMMENT '消息加解密密钥',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='微信公众号配置表';

-- 2. 微信小程序配置表
CREATE TABLE IF NOT EXISTS `config_wechat_miniprogram` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `enabled` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否启用',
  `app_id` varchar(100) NOT NULL DEFAULT '' COMMENT '微信小程序AppID',
  `app_secret` varchar(255) NOT NULL DEFAULT '' COMMENT '微信小程序AppSecret',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='微信小程序配置表';

-- 3. 微信支付配置表
CREATE TABLE IF NOT EXISTS `config_wechat_pay` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `enabled` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否启用',
  `sandbox` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否为沙箱环境',
  `mch_id` varchar(50) NOT NULL DEFAULT '' COMMENT '微信支付商户号',
  `api_v3_key` varchar(255) NOT NULL DEFAULT '' COMMENT 'API v3密钥',
  `api_key` varchar(255) NOT NULL DEFAULT '' COMMENT 'API v2密钥',
  `serial_no` varchar(100) NOT NULL DEFAULT '' COMMENT '证书序列号',
  `certificate_content` text COMMENT '证书内容',
  `private_key_content` text COMMENT '私钥内容',
  `notify_url` varchar(255) NOT NULL DEFAULT '' COMMENT '支付结果通知URL',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='微信支付配置表';

-- 4. 支付宝配置表
CREATE TABLE IF NOT EXISTS `config_alipay` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `enabled` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否启用',
  `sandbox_mode` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否为沙箱模式',
  `app_id` varchar(50) NOT NULL DEFAULT '' COMMENT '支付宝应用ID',
  `private_key` text COMMENT '应用私钥',
  `alipay_public_key` text COMMENT '支付宝公钥',
  `sign_type` varchar(10) NOT NULL DEFAULT 'RSA2' COMMENT '签名类型',
  `notify_url` varchar(255) NOT NULL DEFAULT '' COMMENT '支付结果通知URL',
  `enable_pc_pay` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用电脑网站支付',
  `pc_return_url` varchar(255) NOT NULL DEFAULT '' COMMENT '电脑网站支付同步返回地址',
  `enable_wap_pay` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用手机网站支付',
  `wap_return_url` varchar(255) NOT NULL DEFAULT '' COMMENT '手机网站支付同步返回地址',
  `wap_quit_url` varchar(255) NOT NULL DEFAULT '' COMMENT '手机网站支付中途退出返回地址',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='支付宝配置表';

-- 5. 扣子配置表
CREATE TABLE IF NOT EXISTS `config_kouzi` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `enabled` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否启用',
  `app_id` varchar(100) NOT NULL DEFAULT '' COMMENT '扣子OAuth应用ID',
  `auth_mode` varchar(20) NOT NULL DEFAULT 'jwt' COMMENT '认证方式',
  `private_key` text COMMENT '扣子OAuth应用私钥',
  `public_key` text COMMENT '扣子OAuth应用公钥',
  `personal_access_token` varchar(255) NOT NULL DEFAULT '' COMMENT '个人访问令牌（备用）',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='扣子配置表';

-- 6. 验证配置表（短信、邮箱、对象存储）
CREATE TABLE IF NOT EXISTS `config_verification` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  -- 短信验证配置
  `sms_provider` varchar(20) NOT NULL DEFAULT 'aliyun' COMMENT '短信服务提供商',
  `sms_enabled` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否启用短信验证',
  -- 阿里云短信配置
  `sms_aliyun_access_key_id` varchar(100) NOT NULL DEFAULT '' COMMENT '阿里云短信Access Key ID',
  `sms_aliyun_access_key_secret` varchar(255) NOT NULL DEFAULT '' COMMENT '阿里云短信Access Key Secret',
  `sms_aliyun_sign_name` varchar(50) NOT NULL DEFAULT '' COMMENT '阿里云短信签名',
  `sms_aliyun_template_code` varchar(50) NOT NULL DEFAULT '' COMMENT '阿里云短信验证码模板代码',
  -- 腾讯云短信配置
  `sms_tencent_secret_id` varchar(100) NOT NULL DEFAULT '' COMMENT '腾讯云短信Secret ID',
  `sms_tencent_secret_key` varchar(255) NOT NULL DEFAULT '' COMMENT '腾讯云短信Secret Key',
  `sms_tencent_app_id` varchar(50) NOT NULL DEFAULT '' COMMENT '腾讯云短信应用ID',
  `sms_tencent_sign_name` varchar(50) NOT NULL DEFAULT '' COMMENT '腾讯云短信签名',
  `sms_tencent_template_id` varchar(50) NOT NULL DEFAULT '' COMMENT '腾讯云短信验证码模板ID',
  -- 华为云短信配置
  `sms_huawei_app_key` varchar(100) NOT NULL DEFAULT '' COMMENT '华为云短信App Key',
  `sms_huawei_app_secret` varchar(255) NOT NULL DEFAULT '' COMMENT '华为云短信App Secret',
  `sms_huawei_sign_name` varchar(50) NOT NULL DEFAULT '' COMMENT '华为云短信签名',
  `sms_huawei_template_id` varchar(50) NOT NULL DEFAULT '' COMMENT '华为云短信验证码模板ID',
  -- 邮箱验证配置
  `email_provider` varchar(20) NOT NULL DEFAULT 'smtp' COMMENT '邮件服务提供商',
  `email_enabled` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否启用邮箱验证',
  -- SMTP邮箱配置
  `email_smtp_host` varchar(100) NOT NULL DEFAULT '' COMMENT 'SMTP服务器地址',
  `email_smtp_port` int(11) NOT NULL DEFAULT 587 COMMENT 'SMTP服务器端口',
  `email_smtp_from_email` varchar(100) NOT NULL DEFAULT '' COMMENT 'SMTP发件人邮箱',
  `email_smtp_password` varchar(255) NOT NULL DEFAULT '' COMMENT 'SMTP邮箱密码或授权码',
  `email_smtp_from_name` varchar(100) NOT NULL DEFAULT '' COMMENT 'SMTP发件人名称',
  `email_smtp_ssl` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'SMTP是否启用SSL',
  -- 阿里云邮件推送配置
  `email_aliyun_access_key_id` varchar(100) NOT NULL DEFAULT '' COMMENT '阿里云邮件推送Access Key ID',
  `email_aliyun_access_key_secret` varchar(255) NOT NULL DEFAULT '' COMMENT '阿里云邮件推送Access Key Secret',
  `email_aliyun_from_email` varchar(100) NOT NULL DEFAULT '' COMMENT '阿里云邮件推送发件人邮箱',
  `email_aliyun_from_name` varchar(100) NOT NULL DEFAULT '' COMMENT '阿里云邮件推送发件人名称',
  -- 腾讯云邮件推送配置
  `email_tencent_secret_id` varchar(100) NOT NULL DEFAULT '' COMMENT '腾讯云邮件推送Secret ID',
  `email_tencent_secret_key` varchar(255) NOT NULL DEFAULT '' COMMENT '腾讯云邮件推送Secret Key',
  `email_tencent_from_email` varchar(100) NOT NULL DEFAULT '' COMMENT '腾讯云邮件推送发件人邮箱',
  `email_tencent_from_name` varchar(100) NOT NULL DEFAULT '' COMMENT '腾讯云邮件推送发件人名称',
  -- 对象存储配置
  `storage_provider` varchar(20) NOT NULL DEFAULT 'aliyun' COMMENT '对象存储服务提供商',
  `storage_enabled` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否启用对象存储',
  -- 阿里云OSS配置
  `storage_aliyun_access_key_id` varchar(100) NOT NULL DEFAULT '' COMMENT '阿里云OSS Access Key ID',
  `storage_aliyun_access_key_secret` varchar(255) NOT NULL DEFAULT '' COMMENT '阿里云OSS Access Key Secret',
  `storage_aliyun_bucket` varchar(100) NOT NULL DEFAULT '' COMMENT '阿里云OSS Bucket名称',
  `storage_aliyun_endpoint` varchar(255) NOT NULL DEFAULT '' COMMENT '阿里云OSS访问域名',
  `storage_aliyun_base_url` varchar(255) NOT NULL DEFAULT '' COMMENT '阿里云OSS基础URL',
  -- 腾讯云COS配置
  `storage_tencent_secret_id` varchar(100) NOT NULL DEFAULT '' COMMENT '腾讯云COS Secret ID',
  `storage_tencent_secret_key` varchar(255) NOT NULL DEFAULT '' COMMENT '腾讯云COS Secret Key',
  `storage_tencent_bucket` varchar(100) NOT NULL DEFAULT '' COMMENT '腾讯云COS Bucket名称',
  `storage_tencent_base_url` varchar(255) NOT NULL DEFAULT '' COMMENT '腾讯云COS基础URL',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='验证配置表';

-- 7. 免费额度配置表
CREATE TABLE IF NOT EXISTS `config_free_quota` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用免费额度',
  `new_user_quota` int(11) NOT NULL DEFAULT 100 COMMENT '新用户免费额度',
  `quota_valid_days` int(11) NOT NULL DEFAULT 30 COMMENT '免费额度有效期（天）',
  `max_consume_per_session` int(11) NOT NULL DEFAULT 20 COMMENT '每日最大消耗点数',
  `insufficient_quota_message` varchar(500) NOT NULL DEFAULT '您今日的免费使用额度已用完，请购买套餐获取更多额度' COMMENT '额度不足提示信息',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='免费额度配置表';

-- 8. 推荐奖励配置表
CREATE TABLE IF NOT EXISTS `config_referral` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `enabled` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否启用推荐奖励',
  -- 海报配置
  `poster_background_image` varchar(500) NOT NULL DEFAULT '' COMMENT '海报背景图片URL',
  `poster_qr_code_size` int(11) NOT NULL DEFAULT 202 COMMENT '二维码大小（像素）',
  `poster_qr_code_position_x` int(11) NOT NULL DEFAULT 200 COMMENT '二维码X坐标位置',
  `poster_qr_code_position_y` int(11) NOT NULL DEFAULT 400 COMMENT '二维码Y坐标位置',
  -- 分享配置
  `share_title` varchar(200) NOT NULL DEFAULT 'AI智能体使用邀请' COMMENT '分享标题',
  `share_description` varchar(500) NOT NULL DEFAULT '注册即可获得额外免费使用次数，快来体验智能AI助手！' COMMENT '分享描述',
  -- 奖励配置
  `invite_reward_points` int(11) NOT NULL DEFAULT 100 COMMENT '邀请奖励点数',
  `register_reward_points` int(11) NOT NULL DEFAULT 50 COMMENT '注册奖励点数',
  -- 邀请配置
  `inviter_reward` int(11) NOT NULL DEFAULT 50 COMMENT '邀请人奖励',
  `max_invite_count` int(11) NOT NULL DEFAULT 10 COMMENT '最大邀请次数',
  `reward_valid_days` int(11) NOT NULL DEFAULT 60 COMMENT '奖励有效期（天）',
  `invite_success_message` varchar(500) NOT NULL DEFAULT '恭喜您成功邀请好友，获得{reward}次免费使用奖励！' COMMENT '邀请成功提示信息',
  -- 合伙人配置
  `invitee_reward` int(11) NOT NULL DEFAULT 30 COMMENT '被邀请人奖励',
  `promoter_cashback` decimal(5,2) NOT NULL DEFAULT 5.00 COMMENT '推广人返现比例（%）',
  `promoter_commission` decimal(5,2) NOT NULL DEFAULT 2.00 COMMENT '推广人佣金比例（%）',
  `min_withdrawal` decimal(10,2) NOT NULL DEFAULT 1.00 COMMENT '最低提现金额',
  `withdrawal_channel` varchar(20) NOT NULL DEFAULT 'alipay' COMMENT '提现渠道',
  `withdrawal_fee` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT '提现手续费（%）',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='推荐奖励配置表';

-- 9. 系统配置表
CREATE TABLE IF NOT EXISTS `config_system` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `site_name` varchar(100) NOT NULL DEFAULT 'AI Agent Admin' COMMENT '站点名称',
  `site_logo` varchar(500) NOT NULL DEFAULT '' COMMENT '站点Logo图片URL',
  `site_description` varchar(500) NOT NULL DEFAULT '专业的AI智能体管理平台' COMMENT '站点描述',
  `copyright` varchar(200) NOT NULL DEFAULT '© 2023 AI Agent Admin' COMMENT '版权信息',
  `contact_email` varchar(100) NOT NULL DEFAULT '<EMAIL>' COMMENT '联系邮箱',
  `beian_info` varchar(100) NOT NULL DEFAULT '' COMMENT '备案信息',
  `site_url` varchar(255) NOT NULL DEFAULT '' COMMENT '站点URL',
  `max_login_attempts` int(11) NOT NULL DEFAULT 5 COMMENT '最大登录尝试次数',
  `login_lock_time` int(11) NOT NULL DEFAULT 30 COMMENT '登录锁定时间(分钟)',
  `session_timeout` int(11) NOT NULL DEFAULT 120 COMMENT '会话过期时间(分钟)',
  `maintenance_mode` tinyint(1) NOT NULL DEFAULT 0 COMMENT '维护模式(true/false)',
  `primary_color` varchar(20) NOT NULL DEFAULT '#1890ff' COMMENT '主题色',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- =====================================================
-- 初始化默认配置数据
-- =====================================================

-- 初始化微信公众号配置
INSERT INTO `config_wechat_official` (`enabled`, `app_id`, `app_secret`, `token`, `encoding_aes_key`)
VALUES (0, '', '', '', '')
ON DUPLICATE KEY UPDATE `id` = `id`;

-- 初始化微信小程序配置
INSERT INTO `config_wechat_miniprogram` (`enabled`, `app_id`, `app_secret`)
VALUES (0, '', '')
ON DUPLICATE KEY UPDATE `id` = `id`;

-- 初始化微信支付配置
INSERT INTO `config_wechat_pay` (`enabled`, `sandbox`, `mch_id`, `api_v3_key`, `api_key`, `serial_no`, `notify_url`)
VALUES (0, 1, '', '', '', '', '')
ON DUPLICATE KEY UPDATE `id` = `id`;

-- 初始化支付宝配置
INSERT INTO `config_alipay` (`enabled`, `sandbox_mode`, `app_id`, `sign_type`, `notify_url`, `enable_pc_pay`, `enable_wap_pay`)
VALUES (0, 1, '', 'RSA2', '', 1, 1)
ON DUPLICATE KEY UPDATE `id` = `id`;

-- 初始化扣子配置
INSERT INTO `config_kouzi` (`enabled`, `app_id`, `auth_mode`)
VALUES (0, '', 'jwt')
ON DUPLICATE KEY UPDATE `id` = `id`;

-- 初始化验证配置
INSERT INTO `config_verification` (`sms_provider`, `sms_enabled`, `email_provider`, `email_enabled`, `storage_provider`, `storage_enabled`, `email_smtp_port`, `email_smtp_ssl`)
VALUES ('aliyun', 0, 'smtp', 0, 'aliyun', 0, 587, 1)
ON DUPLICATE KEY UPDATE `id` = `id`;

-- 初始化免费额度配置
INSERT INTO `config_free_quota` (`enabled`, `new_user_quota`, `quota_valid_days`, `max_consume_per_session`, `insufficient_quota_message`)
VALUES (1, 100, 30, 20, '您今日的免费使用额度已用完，请购买套餐获取更多额度')
ON DUPLICATE KEY UPDATE `id` = `id`;

-- 初始化推荐奖励配置
INSERT INTO `config_referral` (`enabled`, `poster_qr_code_size`, `poster_qr_code_position_x`, `poster_qr_code_position_y`, `share_title`, `share_description`, `invite_reward_points`, `register_reward_points`, `inviter_reward`, `max_invite_count`, `reward_valid_days`, `invite_success_message`, `invitee_reward`, `promoter_cashback`, `promoter_commission`, `min_withdrawal`, `withdrawal_channel`, `withdrawal_fee`)
VALUES (0, 202, 200, 400, 'AI智能体使用邀请', '注册即可获得额外免费使用次数，快来体验智能AI助手！', 100, 50, 50, 10, 60, '恭喜您成功邀请好友，获得{reward}次免费使用奖励！', 30, 5.00, 2.00, 1.00, 'alipay', 0.00)
ON DUPLICATE KEY UPDATE `id` = `id`;

-- 初始化系统配置
INSERT INTO `config_system` (`site_name`, `site_description`, `copyright`, `contact_email`, `max_login_attempts`, `login_lock_time`, `session_timeout`, `maintenance_mode`, `primary_color`)
VALUES ('AI Agent Admin', '专业的AI智能体管理平台', '© 2023 AI Agent Admin', '<EMAIL>', 5, 30, 120, 0, '#1890ff')
ON DUPLICATE KEY UPDATE `id` = `id`;
