import { AppDataSource } from '../data-source';

async function testPackagePurchaseFix() {
  try {
    await AppDataSource.initialize();
    console.log('✅ 数据库连接已建立\n');

    const testUserId = 65; // 使用您的用户ID

    // 1. 查看当前用户状态
    console.log('=== 当前用户状态 ===');
    const userInfo = await AppDataSource.query(`
      SELECT m.id, m.name, m.packageId, m.packageExpiredAt, m.balance, m.points,
             p.title, p.dailyMaxConsumption, p.price
      FROM members m
      LEFT JOIN package p ON m.packageId = p.id
      WHERE m.id = ?
    `, [testUserId]);

    if (userInfo.length === 0) {
      console.log('❌ 用户不存在');
      return;
    }

    const user = userInfo[0];
    const isCurrentUnlimited = user.dailyMaxConsumption === 0 || user.dailyMaxConsumption === -1;
    const hasValidPackage = user.packageExpiredAt && new Date(user.packageExpiredAt) > new Date();

    console.log(`用户: ${user.name} (ID: ${user.id})`);
    console.log(`余额: ¥${user.balance}`);
    console.log(`点数: ${user.points}`);
    if (user.packageId) {
      console.log(`当前套餐: ${user.title} (${isCurrentUnlimited ? '无限制' : `限制${user.dailyMaxConsumption}/天`})`);
      console.log(`到期时间: ${user.packageExpiredAt ? new Date(user.packageExpiredAt).toLocaleString() : '无'}`);
      console.log(`套餐状态: ${hasValidPackage ? '有效' : '已过期'}`);
    } else {
      console.log('当前套餐: 无');
    }

    // 2. 查看可用套餐
    console.log('\n=== 可用套餐 ===');
    const packages = await AppDataSource.query(`
      SELECT id, title, price, dailyMaxConsumption, enabled
      FROM package 
      WHERE enabled = 1
      ORDER BY id ASC
    `);

    packages.forEach((pkg: any) => {
      const isUnlimited = pkg.dailyMaxConsumption === 0 || pkg.dailyMaxConsumption === -1;
      console.log(`${pkg.id}. ${pkg.title} - ¥${pkg.price} (${isUnlimited ? '无限制' : `限制${pkg.dailyMaxConsumption}/天`})`);
    });

    // 3. 模拟购买限制检查
    console.log('\n=== 购买限制检查 ===');
    
    // 找一个限制套餐来测试
    const limitedPackage = packages.find((p: any) => p.dailyMaxConsumption > 0);
    if (limitedPackage) {
      console.log(`\n测试购买限制套餐: ${limitedPackage.title}`);
      
      const isTargetUnlimited = limitedPackage.dailyMaxConsumption === 0 || limitedPackage.dailyMaxConsumption === -1;
      
      if (!isTargetUnlimited) {
        if (hasValidPackage) {
          if (user.dailyMaxConsumption > 0) {
            console.log('❌ 预期结果: 应该被阻止 - 当前已有有效的限制套餐');
          } else if (isCurrentUnlimited) {
            console.log('✅ 预期结果: 应该允许 - 当前是无限制套餐，可以购买限制套餐');
          }
        } else {
          console.log('✅ 预期结果: 应该允许 - 当前套餐已过期');
        }
      } else {
        console.log('✅ 预期结果: 应该允许 - 目标是无限制套餐');
      }
    }

    // 4. 测试额度详情显示
    console.log('\n=== 测试额度详情显示 ===');
    
    // 查看用户的套餐购买记录
    const packageRecords = await AppDataSource.query(`
      SELECT pr.amount, pr.description, pr.createdAt, pr.relatedId,
             p.title, p.dailyMaxConsumption
      FROM points_records pr
      LEFT JOIN package p ON pr.relatedId = p.id
      WHERE pr.memberId = ?
      AND pr.type = 'package_purchase'
      AND pr.amount > 0
      ORDER BY pr.createdAt DESC
    `, [testUserId]);

    console.log('套餐购买记录:');
    packageRecords.forEach((record: any, index: number) => {
      const isUnlimited = record.dailyMaxConsumption === 0 || record.dailyMaxConsumption === -1;
      console.log(`  ${index + 1}. ${record.title} - ${record.amount}点数 (${isUnlimited ? '无限制' : `限制${record.dailyMaxConsumption}/天`})`);
      console.log(`     购买时间: ${new Date(record.createdAt).toLocaleString()}`);
    });

    // 统计限制套餐和无限制套餐的点数
    const limitedPoints = packageRecords
      .filter((r: any) => r.dailyMaxConsumption > 0)
      .reduce((sum: number, r: any) => sum + r.amount, 0);
    
    const unlimitedPoints = packageRecords
      .filter((r: any) => r.dailyMaxConsumption === 0 || r.dailyMaxConsumption === -1)
      .reduce((sum: number, r: any) => sum + r.amount, 0);

    console.log(`\n限制套餐总点数: ${limitedPoints}`);
    console.log(`无限制套餐总点数: ${unlimitedPoints}`);

    if (limitedPoints > 0) {
      console.log('✅ 应该在额度详情中显示限制套餐信息');
    }
    if (unlimitedPoints > 0) {
      console.log('✅ 应该在额度详情中显示无限制套餐信息');
    }

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('\n🔌 数据库连接已关闭');
    }
  }
}

// 运行测试
testPackagePurchaseFix().catch(console.error);
