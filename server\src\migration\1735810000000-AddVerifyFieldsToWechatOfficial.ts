import { MigrationInterface, QueryRunner } from "typeorm";

export class AddVerifyFieldsToWechatOfficial1735810000000 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        // 检查字段是否已存在，如果不存在则添加
        const table = await queryRunner.getTable('config_wechat_official');
        
        if (table) {
            // 检查 verify_file_name 字段是否存在
            const verifyFileNameColumn = table.findColumnByName('verify_file_name');
            if (!verifyFileNameColumn) {
                await queryRunner.query(`
                    ALTER TABLE \`config_wechat_official\` 
                    ADD COLUMN \`verify_file_name\` varchar(255) NOT NULL DEFAULT '' COMMENT '域名验证文件名'
                `);
            }
            
            // 检查 verify_file_content 字段是否存在
            const verifyFileContentColumn = table.findColumnByName('verify_file_content');
            if (!verifyFileContentColumn) {
                await queryRunner.query(`
                    ALTER TABLE \`config_wechat_official\` 
                    ADD COLUMN \`verify_file_content\` text COMMENT '域名验证文件内容'
                `);
            }
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // 删除添加的字段
        await queryRunner.query(`ALTER TABLE \`config_wechat_official\` DROP COLUMN \`verify_file_content\``);
        await queryRunner.query(`ALTER TABLE \`config_wechat_official\` DROP COLUMN \`verify_file_name\``);
    }

}
