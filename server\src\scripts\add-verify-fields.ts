import { AppDataSource } from '../data-source';

async function addVerifyFields() {
  try {
    await AppDataSource.initialize();
    console.log('数据库连接成功');

    const queryRunner = AppDataSource.createQueryRunner();
    
    try {
      // 检查并添加 verify_file_name 字段
      const checkVerifyFileName = await queryRunner.query(`
        SELECT COLUMN_NAME 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'config_wechat_official' 
        AND COLUMN_NAME = 'verify_file_name'
      `);
      
      if (checkVerifyFileName.length === 0) {
        console.log('添加 verify_file_name 字段...');
        await queryRunner.query(`
          ALTER TABLE \`config_wechat_official\` 
          ADD COLUMN \`verify_file_name\` varchar(255) NOT NULL DEFAULT '' COMMENT '域名验证文件名'
        `);
        console.log('✅ verify_file_name 字段添加成功');
      } else {
        console.log('verify_file_name 字段已存在');
      }

      // 检查并添加 verify_file_content 字段
      const checkVerifyFileContent = await queryRunner.query(`
        SELECT COLUMN_NAME 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'config_wechat_official' 
        AND COLUMN_NAME = 'verify_file_content'
      `);
      
      if (checkVerifyFileContent.length === 0) {
        console.log('添加 verify_file_content 字段...');
        await queryRunner.query(`
          ALTER TABLE \`config_wechat_official\` 
          ADD COLUMN \`verify_file_content\` text COMMENT '域名验证文件内容'
        `);
        console.log('✅ verify_file_content 字段添加成功');
      } else {
        console.log('verify_file_content 字段已存在');
      }

    } finally {
      await queryRunner.release();
    }

    console.log('✅ 所有字段检查完成');
    
  } catch (error) {
    console.error('❌ 添加字段失败:', error);
  } finally {
    await AppDataSource.destroy();
  }
}

addVerifyFields();
