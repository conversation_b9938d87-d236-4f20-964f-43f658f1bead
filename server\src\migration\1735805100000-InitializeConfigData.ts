import { MigrationInterface, QueryRunner } from "typeorm";

export class InitializeConfigData1735805100000 implements MigrationInterface {
    name = 'InitializeConfigData1735805100000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // 初始化微信公众号配置
        await queryRunner.query(`
            INSERT INTO \`config_wechat_official\` (\`enabled\`, \`app_id\`, \`app_secret\`, \`token\`, \`encoding_aes_key\`) 
            VALUES (0, '', '', '', '') 
            ON DUPLICATE KEY UPDATE \`id\` = \`id\`
        `);

        // 初始化微信小程序配置
        await queryRunner.query(`
            INSERT INTO \`config_wechat_miniprogram\` (\`enabled\`, \`app_id\`, \`app_secret\`) 
            VALUES (0, '', '') 
            ON DUPLICATE KEY UPDATE \`id\` = \`id\`
        `);

        // 初始化微信支付配置
        await queryRunner.query(`
            INSERT INTO \`config_wechat_pay\` (\`enabled\`, \`sandbox\`, \`mch_id\`, \`api_v3_key\`, \`api_key\`, \`serial_no\`, \`notify_url\`) 
            VALUES (0, 1, '', '', '', '', '') 
            ON DUPLICATE KEY UPDATE \`id\` = \`id\`
        `);

        // 初始化支付宝配置
        await queryRunner.query(`
            INSERT INTO \`config_alipay\` (\`enabled\`, \`sandbox_mode\`, \`app_id\`, \`sign_type\`, \`notify_url\`, \`enable_pc_pay\`, \`enable_wap_pay\`) 
            VALUES (0, 1, '', 'RSA2', '', 1, 1) 
            ON DUPLICATE KEY UPDATE \`id\` = \`id\`
        `);

        // 初始化扣子配置
        await queryRunner.query(`
            INSERT INTO \`config_kouzi\` (\`enabled\`, \`app_id\`, \`auth_mode\`) 
            VALUES (0, '', 'jwt') 
            ON DUPLICATE KEY UPDATE \`id\` = \`id\`
        `);

        // 初始化验证配置
        await queryRunner.query(`
            INSERT INTO \`config_verification\` (\`sms_provider\`, \`sms_enabled\`, \`email_provider\`, \`email_enabled\`, \`storage_provider\`, \`storage_enabled\`, \`email_smtp_port\`, \`email_smtp_ssl\`) 
            VALUES ('aliyun', 0, 'smtp', 0, 'aliyun', 0, 587, 1) 
            ON DUPLICATE KEY UPDATE \`id\` = \`id\`
        `);

        // 初始化免费额度配置
        await queryRunner.query(`
            INSERT INTO \`config_free_quota\` (\`enabled\`, \`new_user_quota\`, \`quota_valid_days\`, \`max_consume_per_session\`, \`insufficient_quota_message\`) 
            VALUES (1, 100, 30, 20, '您今日的免费使用额度已用完，请购买套餐获取更多额度') 
            ON DUPLICATE KEY UPDATE \`id\` = \`id\`
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // 清空所有配置表的数据
        await queryRunner.query(`DELETE FROM \`config_free_quota\``);
        await queryRunner.query(`DELETE FROM \`config_verification\``);
        await queryRunner.query(`DELETE FROM \`config_kouzi\``);
        await queryRunner.query(`DELETE FROM \`config_alipay\``);
        await queryRunner.query(`DELETE FROM \`config_wechat_pay\``);
        await queryRunner.query(`DELETE FROM \`config_wechat_miniprogram\``);
        await queryRunner.query(`DELETE FROM \`config_wechat_official\``);
    }
}
