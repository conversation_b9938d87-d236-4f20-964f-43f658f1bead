import { AppDataSource } from '../data-source';

async function testPurchaseRestriction() {
  try {
    await AppDataSource.initialize();
    console.log('✅ 数据库连接已建立\n');

    const testUserId = 65; // 使用您的用户ID

    console.log('=== 测试套餐购买限制 ===');
    
    // 1. 查看当前用户状态
    const userInfo = await AppDataSource.query(`
      SELECT m.id, m.name, m.packageId, m.packageExpiredAt, m.balance,
             p.title, p.dailyMaxConsumption
      FROM members m
      LEFT JOIN package p ON m.packageId = p.id
      WHERE m.id = ?
    `, [testUserId]);

    const user = userInfo[0];
    const hasValidPackage = user.packageExpiredAt && new Date(user.packageExpiredAt) > new Date();
    const isCurrentUnlimited = user.dailyMaxConsumption === 0 || user.dailyMaxConsumption === -1;

    console.log(`当前用户: ${user.name} (ID: ${user.id})`);
    console.log(`当前套餐: ${user.title || '无'}`);
    console.log(`套餐类型: ${isCurrentUnlimited ? '无限制' : `限制${user.dailyMaxConsumption}/天`}`);
    console.log(`套餐状态: ${hasValidPackage ? '有效' : '已过期'}`);
    console.log(`余额: ¥${user.balance}`);

    // 2. 找一个限制套餐来测试
    const limitedPackages = await AppDataSource.query(`
      SELECT id, title, price, dailyMaxConsumption
      FROM package 
      WHERE enabled = 1 AND dailyMaxConsumption > 0
      ORDER BY id ASC
      LIMIT 1
    `);

    if (limitedPackages.length === 0) {
      console.log('❌ 没有找到限制套餐');
      return;
    }

    const targetPackage = limitedPackages[0];
    console.log(`\n测试购买限制套餐: ${targetPackage.title} (限制${targetPackage.dailyMaxConsumption}/天)`);

    // 3. 模拟购买限制检查逻辑
    console.log('\n=== 购买限制检查逻辑 ===');
    
    const isTargetUnlimited = targetPackage.dailyMaxConsumption === 0 || targetPackage.dailyMaxConsumption === -1;
    
    if (!isTargetUnlimited) {
      console.log('目标套餐是限制套餐，检查购买限制...');
      
      if (hasValidPackage) {
        console.log('用户当前有有效套餐');
        
        if (user.dailyMaxConsumption > 0) {
          console.log('❌ 购买限制: 当前已有有效的限制套餐');
          console.log(`   当前套餐: ${user.title} (限制${user.dailyMaxConsumption}/天)`);
          console.log(`   到期时间: ${new Date(user.packageExpiredAt).toLocaleString()}`);
          console.log('   结果: 应该被阻止购买');
        } else if (isCurrentUnlimited) {
          console.log('✅ 允许购买: 当前是无限制套餐，可以购买限制套餐');
          console.log(`   当前套餐: ${user.title} (无限制)`);
          console.log('   结果: 应该允许购买（会覆盖当前套餐）');
        }
      } else {
        console.log('✅ 允许购买: 当前套餐已过期');
        console.log('   结果: 应该允许购买');
      }
    } else {
      console.log('✅ 允许购买: 目标是无限制套餐');
      console.log('   结果: 应该允许购买');
    }

    // 4. 测试不同场景
    console.log('\n=== 测试不同场景 ===');
    
    // 场景1: 当前无限制套餐 -> 购买限制套餐
    if (hasValidPackage && isCurrentUnlimited) {
      console.log('场景1: 无限制套餐用户购买限制套餐');
      console.log('  预期结果: ✅ 允许购买');
      console.log('  实际逻辑: 当前是无限制套餐，可以购买限制套餐');
    }
    
    // 场景2: 当前限制套餐 -> 购买限制套餐
    console.log('\n场景2: 限制套餐用户购买限制套餐');
    console.log('  预期结果: ❌ 禁止购买');
    console.log('  实际逻辑: 已有有效的限制套餐，需要等待到期');
    
    // 场景3: 无套餐或过期套餐 -> 购买限制套餐
    console.log('\n场景3: 无套餐或过期套餐用户购买限制套餐');
    console.log('  预期结果: ✅ 允许购买');
    console.log('  实际逻辑: 没有有效套餐，可以购买');
    
    // 场景4: 任何用户 -> 购买无限制套餐
    console.log('\n场景4: 任何用户购买无限制套餐');
    console.log('  预期结果: ✅ 允许购买');
    console.log('  实际逻辑: 无限制套餐没有购买限制');

    // 5. 创建一个测试用户来验证限制套餐购买限制
    console.log('\n=== 创建测试用户验证限制 ===');
    
    const testUserId2 = 999;
    
    // 删除可能存在的测试用户
    await AppDataSource.query(`DELETE FROM members WHERE id = ?`, [testUserId2]);
    
    // 创建测试用户并给他一个限制套餐
    await AppDataSource.query(`
      INSERT INTO members (id, name, password, email, phone, balance, points, packageId, packageExpiredAt)
      VALUES (?, 'test_limited_user', 'password123', '<EMAIL>', '13800138000', 10000, 1000, ?, DATE_ADD(NOW(), INTERVAL 30 DAY))
    `, [testUserId2, targetPackage.id]);
    
    console.log(`创建测试用户 ${testUserId2}，当前套餐: ${targetPackage.title} (限制${targetPackage.dailyMaxConsumption}/天)`);
    
    // 模拟这个用户尝试购买另一个限制套餐
    const anotherLimitedPackage = await AppDataSource.query(`
      SELECT id, title, dailyMaxConsumption
      FROM package 
      WHERE enabled = 1 AND dailyMaxConsumption > 0 AND id != ?
      LIMIT 1
    `, [targetPackage.id]);
    
    if (anotherLimitedPackage.length > 0) {
      const anotherPackage = anotherLimitedPackage[0];
      console.log(`测试用户尝试购买: ${anotherPackage.title} (限制${anotherPackage.dailyMaxConsumption}/天)`);
      console.log('预期结果: ❌ 应该被阻止，因为已有有效的限制套餐');
    }
    
    // 清理测试用户
    await AppDataSource.query(`DELETE FROM members WHERE id = ?`, [testUserId2]);
    console.log('测试用户已清理');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('\n🔌 数据库连接已关闭');
    }
  }
}

// 运行测试
testPurchaseRestriction().catch(console.error);
