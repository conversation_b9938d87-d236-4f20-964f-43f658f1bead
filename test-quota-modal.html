<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>额度详情弹窗测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .test-container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .user-stats {
            display: flex;
            justify-content: space-around;
            margin-bottom: 20px;
            padding: 20px;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border-radius: 15px;
            color: white;
        }

        .stat-item {
            text-align: center;
            cursor: pointer;
            padding: 10px;
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .stat-item:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.05);
        }

        .stat-value {
            display: block;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 14px;
            opacity: 0.9;
        }

        .test-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .test-info h3 {
            margin: 0 0 10px 0;
            color: #333;
        }

        .test-info p {
            margin: 5px 0;
            color: #666;
            font-size: 14px;
        }

        .api-test-btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            margin-bottom: 10px;
        }

        .api-test-btn:hover {
            opacity: 0.9;
        }

        .result-area {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
        }

        .result-area pre {
            margin: 0;
            font-size: 12px;
            color: #333;
            white-space: pre-wrap;
        }

        .login-info {
            background: #e3f2fd;
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 15px;
            font-size: 14px;
            color: #1976d2;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h2 style="text-align: center; color: #333; margin-bottom: 20px;">额度详情弹窗测试</h2>
        
        <div class="login-info">
            💡 提示：请先在个人中心页面登录，然后回到此页面测试API
        </div>

        <!-- 模拟用户统计区域 -->
        <div class="user-stats">
            <div class="stat-item">
                <span class="stat-value">100</span>
                <span class="stat-label">余额</span>
            </div>
            <div class="stat-item" onclick="testQuotaAPI()">
                <span class="stat-value" id="pointsValue">1010</span>
                <span class="stat-label">点数</span>
            </div>
            <div class="stat-item">
                <span class="stat-value">5</span>
                <span class="stat-label">邀请</span>
            </div>
        </div>

        <div class="test-info">
            <h3>🧪 测试说明</h3>
            <p>• 点击"点数"区域测试额度详情API</p>
            <p>• 或点击下方按钮直接测试API</p>
            <p>• 需要先登录才能获取用户数据</p>
        </div>

        <button class="api-test-btn" onclick="testQuotaAPI()">
            📊 测试额度详情API
        </button>

        <button class="api-test-btn" onclick="openProfilePage()">
            👤 打开个人中心页面
        </button>

        <div class="result-area" id="resultArea">
            <pre>点击上方按钮开始测试...</pre>
        </div>
    </div>

    <script>
        // 测试额度详情API
        async function testQuotaAPI() {
            const resultArea = document.getElementById('resultArea');
            resultArea.innerHTML = '<pre>🔄 正在获取额度详情...</pre>';

            try {
                // 获取存储的token
                const token = localStorage.getItem('token') || sessionStorage.getItem('token');
                
                if (!token) {
                    resultArea.innerHTML = '<pre style="color: #f44336;">❌ 未找到登录token，请先登录</pre>';
                    return;
                }

                // 调用额度详情API
                const response = await fetch('http://localhost:3030/api/daily-limit/stats', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();
                
                if (response.ok && data.code === 200) {
                    // 更新点数显示
                    document.getElementById('pointsValue').textContent = data.data.totalUsage || 0;
                    
                    // 显示详细结果
                    const result = {
                        '✅ API调用成功': true,
                        '📊 额度详情': {
                            '总使用': `${data.data.totalUsage} 点数`,
                            '聊天使用': `${data.data.chatUsage} 点数`,
                            '工作流使用': `${data.data.workflowUsage} 点数`,
                            '每日限制': data.data.dailyLimit === -1 ? '无限制' : `${data.data.dailyLimit} 点数`,
                            '剩余额度': data.data.remaining === -1 ? '无限制' : `${data.data.remaining} 点数`,
                            'VIP用户': data.data.isVipUser ? '是' : '否'
                        }
                    };

                    if (data.data.packageTitle) {
                        result['📦 套餐信息'] = {
                            '套餐名称': data.data.packageTitle,
                            '到期时间': data.data.packageExpiredAt ? new Date(data.data.packageExpiredAt).toLocaleString() : '无'
                        };
                    }

                    if (data.data.freeQuotaConfig) {
                        result['🎁 免费额度配置'] = {
                            '启用状态': data.data.freeQuotaConfig.enabled ? '是' : '否',
                            '每日限制': `${data.data.freeQuotaConfig.maxConsumePerSession} 点数`,
                            '不足提示': data.data.freeQuotaConfig.insufficientQuotaMessage
                        };
                    }

                    resultArea.innerHTML = `<pre style="color: #4caf50;">${JSON.stringify(result, null, 2)}</pre>`;
                } else {
                    resultArea.innerHTML = `<pre style="color: #f44336;">❌ API调用失败: ${data.message || '未知错误'}</pre>`;
                }
            } catch (error) {
                resultArea.innerHTML = `<pre style="color: #f44336;">❌ 网络错误: ${error.message}</pre>`;
            }
        }

        // 打开个人中心页面
        function openProfilePage() {
            window.open('http://localhost:3000/#/pages/profile/index', '_blank');
        }

        // 页面加载时检查登录状态
        window.onload = function() {
            const token = localStorage.getItem('token') || sessionStorage.getItem('token');
            if (token) {
                document.querySelector('.login-info').innerHTML = '✅ 已检测到登录token，可以开始测试';
                document.querySelector('.login-info').style.background = '#e8f5e8';
                document.querySelector('.login-info').style.color = '#2e7d32';
            }
        };
    </script>
</body>
</html>
