/**
 * 免费额度配置实体类
 */
import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('config_free_quota')
export class ConfigFreeQuota {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'tinyint', width: 1, default: 1, comment: '是否启用免费额度' })
  enabled: boolean;

  @Column({ name: 'new_user_quota', type: 'int', default: 100, comment: '新用户免费额度' })
  newUserQuota: number;

  @Column({ name: 'quota_valid_days', type: 'int', default: 30, comment: '免费额度有效期（天）' })
  quotaValidDays: number;

  @Column({ name: 'max_consume_per_session', type: 'int', default: 20, comment: '每日最大消耗点数' })
  maxConsumePerSession: number;

  @Column({ name: 'insufficient_quota_message', length: 500, default: '您今日的免费使用额度已用完，请购买套餐获取更多额度', comment: '额度不足提示信息' })
  insufficientQuotaMessage: string;

  @CreateDateColumn({ name: 'created_at', comment: '创建时间' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', comment: '更新时间' })
  updatedAt: Date;
}
