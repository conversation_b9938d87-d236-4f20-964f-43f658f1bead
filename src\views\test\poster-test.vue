<template>
  <div class="poster-test-page">
    <n-card title="推广海报功能测试">
      <n-space vertical size="large">
        <n-alert type="info">
          <template #header>测试说明</template>
          <p>这个页面用于测试推广海报生成功能</p>
          <ul>
            <li>点击"生成推广海报"按钮测试海报生成器</li>
            <li>测试邀请码生成和二维码显示</li>
            <li>测试海报保存和分享功能</li>
          </ul>
        </n-alert>

        <n-card title="海报配置">
          <n-form>
            <n-form-item label="背景图片">
              <n-input v-model:value="posterConfig.backgroundImage" placeholder="输入背景图片URL" />
            </n-form-item>
            <n-form-item label="二维码大小">
              <n-input-number v-model:value="posterConfig.qrCodeSize" :min="50" :max="300" />
            </n-form-item>
            <n-form-item label="二维码位置X">
              <n-input-number v-model:value="posterConfig.qrCodePosition.x" :min="0" :max="500" />
            </n-form-item>
            <n-form-item label="二维码位置Y">
              <n-input-number v-model:value="posterConfig.qrCodePosition.y" :min="0" :max="700" />
            </n-form-item>
          </n-form>
        </n-card>

        <n-card title="分享配置">
          <n-form>
            <n-form-item label="分享标题">
              <n-input v-model:value="shareConfig.title" />
            </n-form-item>
            <n-form-item label="分享描述">
              <n-input v-model:value="shareConfig.description" type="textarea" />
            </n-form-item>
          </n-form>
        </n-card>

        <n-button type="primary" size="large" @click="showPosterGenerator = true">
          <template #icon>
            <n-icon><QrCodeOutline /></n-icon>
          </template>
          生成推广海报
        </n-button>
      </n-space>
    </n-card>

    <!-- 海报生成器 -->
    <ReferralPosterGenerator
      v-model:show="showPosterGenerator"
      :poster-config="posterConfig"
      :share-config="shareConfig"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import {
  NCard,
  NSpace,
  NAlert,
  NForm,
  NFormItem,
  NInput,
  NInputNumber,
  NButton,
  NIcon
} from 'naive-ui'
import { QrCodeOutline } from '@vicons/ionicons5'
import ReferralPosterGenerator from '@/components/ReferralPosterGenerator.vue'
import axios from 'axios'

const showPosterGenerator = ref(false)

const posterConfig = reactive({
  backgroundImage: '',
  qrCodeSize: 202,
  qrCodePosition: { x: 200, y: 400 }
})

const shareConfig = reactive({
  title: 'AI智能体使用邀请',
  description: '注册即可获得额外免费使用次数，快来体验智能AI助手！'
})

// 加载推广配置
const loadReferralConfig = async () => {
  try {
    const response = await axios.get('/api/config/referral')
    if (response.data && response.data.success) {
      const config = response.data.data

      // 更新海报配置
      posterConfig.backgroundImage = config.poster_background_image || ''
      posterConfig.qrCodeSize = parseInt(config.poster_qr_code_size) || 202
      posterConfig.qrCodePosition.x = parseInt(config.poster_qr_code_position_x) || 200
      posterConfig.qrCodePosition.y = parseInt(config.poster_qr_code_position_y) || 400

      // 更新分享配置
      shareConfig.title = config.share_title || 'AI智能体使用邀请'
      shareConfig.description = config.share_description || '注册即可获得额外免费使用次数，快来体验智能AI助手！'

      console.log('推广配置加载成功:', { posterConfig, shareConfig })
    }
  } catch (error) {
    console.error('加载推广配置失败:', error)
  }
}

onMounted(() => {
  loadReferralConfig()
})
</script>

<style scoped>
.poster-test-page {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}
</style>
