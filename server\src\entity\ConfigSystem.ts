/**
 * 系统配置实体类
 */
import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('config_system')
export class ConfigSystem {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'site_name', length: 100, default: 'AI Agent Admin', comment: '站点名称' })
  siteName: string;

  @Column({ name: 'site_logo', length: 500, default: '', comment: '站点Logo图片URL' })
  siteLogo: string;

  @Column({ name: 'main_logo', length: 500, default: '', comment: '主Logo图片URL' })
  mainLogo: string;

  @Column({ name: 'sidebar_logo', length: 500, default: '', comment: '侧边栏Logo图片URL' })
  sidebarLogo: string;

  @Column({ name: 'mini_logo', length: 500, default: '', comment: '迷你Logo图片URL' })
  miniLogo: string;

  @Column({ name: 'user_logo', length: 500, default: '', comment: '用户Logo图片URL' })
  userLogo: string;

  @Column({ name: 'favicon', length: 500, default: '', comment: '网站图标URL' })
  favicon: string;

  @Column({ name: 'customer_service_image', length: 500, default: '', comment: '客服图片URL' })
  customerServiceImage: string;

  @Column({ name: 'site_description', length: 500, default: '专业的AI智能体管理平台', comment: '站点描述' })
  siteDescription: string;

  @Column({ length: 200, default: '© 2023 AI Agent Admin', comment: '版权信息' })
  copyright: string;

  @Column({ name: 'contact_email', length: 100, default: '<EMAIL>', comment: '联系邮箱' })
  contactEmail: string;

  @Column({ name: 'beian_info', length: 100, default: '', comment: '备案信息' })
  beianInfo: string;

  @Column({ name: 'site_url', length: 255, default: '', comment: '站点URL' })
  siteUrl: string;

  @Column({ name: 'max_login_attempts', type: 'int', default: 5, comment: '最大登录尝试次数' })
  maxLoginAttempts: number;

  @Column({ name: 'login_lock_time', type: 'int', default: 30, comment: '登录锁定时间(分钟)' })
  loginLockTime: number;

  @Column({ name: 'session_timeout', type: 'int', default: 120, comment: '会话过期时间(分钟)' })
  sessionTimeout: number;

  @Column({ name: 'maintenance_mode', type: 'tinyint', width: 1, default: 0, comment: '维护模式(true/false)' })
  maintenanceMode: boolean;

  @Column({ name: 'primary_color', length: 20, default: '#1890ff', comment: '主题色' })
  primaryColor: string;

  @CreateDateColumn({ name: 'created_at', comment: '创建时间' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', comment: '更新时间' })
  updatedAt: Date;
}
