const mysql = require('mysql2/promise');

async function checkKouziConfig() {
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'root',
      database: 'ai_agent'
    });

    console.log('检查扣子配置表是否存在...');
    try {
      const [tables] = await connection.execute("SHOW TABLES LIKE 'config_kouzi'");
      console.log('表存在检查结果:', tables);

      if (tables.length === 0) {
        console.log('config_kouzi 表不存在！');
        return;
      }

      console.log('查询扣子配置表结构...');
      const [structure] = await connection.execute('DESCRIBE config_kouzi');
      console.log('表结构:', structure);

      console.log('查询扣子配置数据...');
      const [rows] = await connection.execute('SELECT * FROM config_kouzi ORDER BY id DESC LIMIT 5');
    } catch (tableError) {
      console.error('表操作错误:', tableError.message);
      return;
    }
    
      if (rows.length > 0) {
        console.log('扣子配置数据:');
        rows.forEach((row, index) => {
          console.log(`记录 ${index + 1}:`, JSON.stringify(row, null, 2));
        });
      } else {
        console.log('扣子配置表为空');
      }
    
    await connection.end();
  } catch (error) {
    console.error('查询失败:', error.message);
  }
}

checkKouziConfig();
