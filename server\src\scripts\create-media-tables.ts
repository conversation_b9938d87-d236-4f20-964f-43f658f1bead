import mysql from 'mysql2/promise';
import { AppDataSource } from '../data-source';

const DB_HOST = process.env.DB_HOST || 'localhost';
const DB_PORT = process.env.DB_PORT || '3306';
const DB_USERNAME = process.env.DB_USERNAME || 'root';
const DB_PASSWORD = process.env.DB_PASSWORD || 'root';
const DB_DATABASE = process.env.DB_DATABASE || 'ai_agent';

/**
 * 创建媒体相关表
 */
async function createMediaTables() {
  try {
    // 创建MySQL连接
    const connection = await mysql.createConnection({
      host: DB_HOST,
      port: parseInt(DB_PORT),
      user: DB_USERNAME,
      password: DB_PASSWORD,
      database: DB_DATABASE,
      charset: 'utf8mb4'
    });
    
    console.log('数据库连接成功');
    
    // 检查agent_media表是否存在
    const [mediaRows] = await connection.query(`
      SELECT COUNT(*) as count 
      FROM information_schema.tables 
      WHERE table_schema = ? 
      AND table_name = 'agent_media'
    `, [DB_DATABASE]);

    // @ts-ignore
    const mediaTableExists = mediaRows[0].count > 0;
    console.log(`agent_media表是否存在: ${mediaTableExists ? '是' : '否'}`);

    if (!mediaTableExists) {
      console.log('创建agent_media表...');
      await connection.query(`
        CREATE TABLE agent_media (
          id VARCHAR(36) PRIMARY KEY,
          name VARCHAR(255) NOT NULL,
          url VARCHAR(500) NOT NULL,
          thumbnailUrl VARCHAR(500),
          type ENUM('image', 'video') NOT NULL,
          \`group\` VARCHAR(36) DEFAULT 'ungroup',
          size INT,
          duration INT,
          createTime TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
      `);
      console.log('agent_media表创建成功');
    }

    // 检查agent_media_group表是否存在
    const [groupRows] = await connection.query(`
      SELECT COUNT(*) as count 
      FROM information_schema.tables 
      WHERE table_schema = ? 
      AND table_name = 'agent_media_group'
    `, [DB_DATABASE]);

    // @ts-ignore
    const groupTableExists = groupRows[0].count > 0;
    console.log(`agent_media_group表是否存在: ${groupTableExists ? '是' : '否'}`);

    if (!groupTableExists) {
      console.log('创建agent_media_group表...');
      await connection.query(`
        CREATE TABLE agent_media_group (
          id VARCHAR(36) PRIMARY KEY,
          name VARCHAR(255) NOT NULL,
          createTime TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
      `);
      console.log('agent_media_group表创建成功');
    }

    // 关闭连接
    await connection.end();
    console.log('数据库连接已关闭');
    
  } catch (error) {
    console.error('创建媒体表失败:', error);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  createMediaTables()
    .then(() => {
      console.log('媒体表创建完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('媒体表创建失败:', error);
      process.exit(1);
    });
}

export { createMediaTables };
