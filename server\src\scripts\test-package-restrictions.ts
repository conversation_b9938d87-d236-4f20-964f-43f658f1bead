import { AppDataSource } from '../data-source';

async function testPackageRestrictions() {
  console.log('🧪 开始测试套餐购买限制...\n');

  try {
    // 初始化数据库连接
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
      console.log('✅ 数据库连接已建立\n');
    }

    // 查看现有套餐
    console.log('1. 查看现有套餐...');
    const packages = await AppDataSource.query(`
      SELECT id, title, price, duration, totalQuota, dailyMaxConsumption, enabled
      FROM package 
      WHERE enabled = 1
      ORDER BY id ASC
    `);

    console.log('📦 可用套餐:');
    packages.forEach((pkg: any) => {
      const isUnlimited = pkg.dailyMaxConsumption === 0 || pkg.dailyMaxConsumption === -1;
      console.log(`  ${pkg.id}. ${pkg.title} - 价格: ¥${pkg.price}, 每日限制: ${pkg.dailyMaxConsumption} (${isUnlimited ? '无限制' : '有限制'})`);
    });

    // 查看测试用户
    console.log('\n2. 查看测试用户...');
    const users = await AppDataSource.query(`
      SELECT id, name, points, balance, packageId, packageExpiredAt
      FROM members 
      WHERE id IN (59, 60, 61, 62)
      ORDER BY id ASC
    `);

    console.log('👥 测试用户:');
    users.forEach((user: any) => {
      const hasValidPackage = user.packageId && user.packageExpiredAt && new Date(user.packageExpiredAt) > new Date();
      console.log(`  ${user.id}. ${user.name} - 余额: ¥${user.balance}, 点数: ${user.points}, 套餐: ${user.packageId || '无'} ${hasValidPackage ? '(有效)' : '(无效/过期)'}`);
    });

    // 测试场景1：有限制套餐用户尝试购买另一个有限制套餐
    console.log('\n=== 测试场景 1: 有限制套餐用户购买限制套餐 ===');
    const vipUser = users.find((u: any) => u.id === 59);
    if (vipUser && vipUser.packageId) {
      console.log(`用户 ${vipUser.name} 当前有套餐 ${vipUser.packageId}`);
      
      // 查看当前套餐详情
      const currentPackage = await AppDataSource.query(`
        SELECT id, title, dailyMaxConsumption
        FROM package 
        WHERE id = ?
      `, [vipUser.packageId]);
      
      if (currentPackage.length > 0) {
        const pkg = currentPackage[0];
        const isCurrentUnlimited = pkg.dailyMaxConsumption === 0 || pkg.dailyMaxConsumption === -1;
        console.log(`当前套餐: ${pkg.title}, 每日限制: ${pkg.dailyMaxConsumption} (${isCurrentUnlimited ? '无限制' : '有限制'})`);
        
        if (!isCurrentUnlimited) {
          console.log('✅ 用户有有效的限制套餐，应该被阻止购买新的限制套餐');
        } else {
          console.log('ℹ️ 用户有无限制套餐，可以购买限制套餐');
        }
      }
    }

    // 测试场景2：免费用户购买套餐
    console.log('\n=== 测试场景 2: 免费用户购买套餐 ===');
    const freeUser = users.find((u: any) => u.id === 60);
    if (freeUser && !freeUser.packageId) {
      console.log(`用户 ${freeUser.name} 当前无套餐，可以购买任何套餐`);
    }

    // 测试场景3：创建测试套餐
    console.log('\n=== 测试场景 3: 创建测试套餐 ===');
    
    // 创建一个无限制套餐用于测试
    try {
      await AppDataSource.query(`
        INSERT INTO package (title, price, duration, totalQuota, dailyMaxConsumption, description, enabled, frontendDisplay, \`order\`)
        VALUES ('测试无限制套餐', 999.00, 30, 10000, 0, '测试用无限制套餐', 1, 0, 999)
        ON DUPLICATE KEY UPDATE title = VALUES(title)
      `);
      console.log('✅ 创建测试无限制套餐成功');
    } catch (error) {
      console.log('ℹ️ 测试套餐可能已存在');
    }

    // 创建一个有限制套餐用于测试
    try {
      await AppDataSource.query(`
        INSERT INTO package (title, price, duration, totalQuota, dailyMaxConsumption, description, enabled, frontendDisplay, \`order\`)
        VALUES ('测试限制套餐', 199.00, 30, 1000, 50, '测试用限制套餐', 1, 0, 998)
        ON DUPLICATE KEY UPDATE title = VALUES(title)
      `);
      console.log('✅ 创建测试限制套餐成功');
    } catch (error) {
      console.log('ℹ️ 测试套餐可能已存在');
    }

    // 查看更新后的套餐列表
    console.log('\n4. 更新后的套餐列表...');
    const updatedPackages = await AppDataSource.query(`
      SELECT id, title, price, duration, totalQuota, dailyMaxConsumption, enabled
      FROM package 
      WHERE enabled = 1
      ORDER BY \`order\` ASC, id ASC
    `);

    console.log('📦 所有可用套餐:');
    updatedPackages.forEach((pkg: any) => {
      const isUnlimited = pkg.dailyMaxConsumption === 0 || pkg.dailyMaxConsumption === -1;
      console.log(`  ${pkg.id}. ${pkg.title} - 价格: ¥${pkg.price}, 每日限制: ${pkg.dailyMaxConsumption} (${isUnlimited ? '无限制' : '有限制'})`);
    });

    console.log('\n✅ 套餐限制测试完成！');
    console.log('\n📋 业务规则总结:');
    console.log('1. 有限制套餐 (dailyMaxConsumption > 0): 用户只能同时拥有一个，需要到期后才能购买新的');
    console.log('2. 无限制套餐 (dailyMaxConsumption = 0 或 -1): 用户可以随时购买，不受限制');
    console.log('3. 免费用户: 可以购买任何套餐');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    // 关闭数据库连接
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('\n🔌 数据库连接已关闭');
    }
  }
}

// 运行测试
testPackageRestrictions().catch(console.error);
