import { AppDataSource } from '../data-source';

async function initFreeQuotaConfig() {
  console.log('🔧 开始初始化免费额度配置...\n');

  try {
    // 初始化数据库连接
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
      console.log('✅ 数据库连接已建立\n');
    }

    // 检查system_config表是否存在
    console.log('1. 检查system_config表...');
    const tableExists = await AppDataSource.query(`
      SELECT EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_schema = DATABASE() 
        AND table_name = 'system_config'
      ) as \`exists\`
    `);

    if (!tableExists[0].exists) {
      console.log('创建system_config表...');
      await AppDataSource.query(`
        CREATE TABLE system_config (
          id INT AUTO_INCREMENT PRIMARY KEY,
          type VARCHAR(50) NOT NULL,
          \`key\` VARCHAR(100) NOT NULL,
          value TEXT,
          description VARCHAR(255),
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          UNIQUE KEY \`uk_type_key\` (\`type\`, \`key\`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      `);
      console.log('✅ system_config表创建成功');
    } else {
      console.log('✅ system_config表已存在');
    }

    // 插入免费额度配置
    console.log('\n2. 插入免费额度配置...');
    await AppDataSource.query(`
      INSERT INTO \`system_config\` (\`type\`, \`key\`, \`value\`, \`description\`) VALUES
      ('free-quota', 'enabled', 'true', '是否启用免费额度'),
      ('free-quota', 'new_user_quota', '100', '新用户免费额度'),
      ('free-quota', 'quota_valid_days', '30', '免费额度有效期（天）'),
      ('free-quota', 'max_consume_per_session', '20', '每日最大消耗点数'),
      ('free-quota', 'insufficient_quota_message', '您今日的免费使用额度已用完，请购买套餐获取更多额度', '额度不足提示信息')
      ON DUPLICATE KEY UPDATE 
      \`value\` = VALUES(\`value\`),
      \`description\` = VALUES(\`description\`)
    `);
    console.log('✅ 免费额度配置插入成功');

    // 查询插入结果
    console.log('\n3. 查询配置结果...');
    const configs = await AppDataSource.query(`
      SELECT \`type\`, \`key\`, \`value\`, \`description\`
      FROM \`system_config\`
      WHERE \`type\` = 'free-quota'
      ORDER BY \`key\`
    `);

    console.log('📋 免费额度配置:');
    configs.forEach((config: any) => {
      console.log(`  ${config.key}: ${config.value} (${config.description})`);
    });

    console.log('\n✅ 免费额度配置初始化完成！');

  } catch (error) {
    console.error('❌ 初始化失败:', error);
  } finally {
    // 关闭数据库连接
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('\n🔌 数据库连接已关闭');
    }
  }
}

// 运行初始化
initFreeQuotaConfig().catch(console.error);
