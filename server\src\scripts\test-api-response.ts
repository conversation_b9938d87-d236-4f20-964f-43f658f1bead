import { AppDataSource } from '../data-source'
import { DailyLimitService } from '../services/daily-limit.service'

async function testApiResponse() {
  try {
    await AppDataSource.initialize()
    console.log('数据库连接成功')

    const testMemberId = 1

    console.log(`\n=== 测试用户 ${testMemberId} 的API响应 ===`)

    // 1. 测试 getQuotaDetailsByCategory 方法
    console.log('\n1. 测试 getQuotaDetailsByCategory:')
    const quotaDetails = await DailyLimitService.getQuotaDetailsByCategory(testMemberId)
    console.log('返回结果:', JSON.stringify(quotaDetails, null, 2))

    // 2. 检查用户的套餐信息
    console.log('\n2. 检查用户套餐信息:')
    const userInfo = await AppDataSource.query(`
      SELECT m.id, m.name, m.packageId, m.packageExpiredAt, p.title, p.dailyMaxConsumption
      FROM members m
      LEFT JOIN package p ON m.packageId = p.id
      WHERE m.id = ?
    `, [testMemberId])
    console.log('用户信息:', userInfo)

    // 3. 检查无限制套餐查询
    console.log('\n3. 检查无限制套餐查询:')
    const unlimitedQuery = await AppDataSource.query(`
      SELECT p.id, p.title, p.dailyMaxConsumption, m.packageExpiredAt
      FROM members m
      LEFT JOIN package p ON m.packageId = p.id
      WHERE m.id = ?
      AND m.packageId IS NOT NULL
      AND m.packageExpiredAt > NOW()
      AND p.dailyMaxConsumption IN (0, -1)
    `, [testMemberId])
    console.log('无限制套餐查询结果:', unlimitedQuery)

    // 4. 检查无限制套餐统计
    console.log('\n4. 检查无限制套餐统计:')
    const unlimitedStats = await DailyLimitService.getUnlimitedPackageStats(testMemberId)
    console.log('无限制套餐统计:', unlimitedStats)

    // 5. 检查今日使用量
    console.log('\n5. 检查今日使用量:')
    const todayUsed = await DailyLimitService.getTodayUsedPoints(testMemberId)
    console.log('今日使用量:', todayUsed)

    // 6. 检查免费额度配置
    console.log('\n6. 检查免费额度配置:')
    const freeQuotaConfig = await DailyLimitService.getFreeQuotaConfig()
    console.log('免费额度配置:', freeQuotaConfig)

  } catch (error) {
    console.error('测试失败:', error)
  } finally {
    await AppDataSource.destroy()
  }
}

testApiResponse()
