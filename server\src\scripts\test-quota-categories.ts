import { AppDataSource } from '../data-source';

async function testQuotaCategories() {
  console.log('🧪 开始测试分类额度功能...\n');

  try {
    // 初始化数据库连接
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
      console.log('✅ 数据库连接已建立\n');
    }

    // 导入DailyLimitService
    const { DailyLimitService } = await import('../services/daily-limit.service');

    console.log('=== 测试分类额度API ===\n');

    // 获取测试用户
    const users = await AppDataSource.query(`
      SELECT id, name, points, packageId, packageExpiredAt
      FROM members
      WHERE id IN (59, 60, 61)
      ORDER BY id ASC
    `);

    for (const user of users) {
      console.log(`\n=== 测试用户 ${user.id} (${user.name}) ===`);
      console.log(`当前点数: ${user.points}, 套餐ID: ${user.packageId || '无'}`);

      try {
        // 测试新的分类额度API
        const quotaDetails = await DailyLimitService.getQuotaDetailsByCategory(user.id);
        
        console.log('\n📊 分类额度详情:');
        
        // 1. 免费额度
        console.log('\n🎁 免费额度 (优先级1):');
        console.log(`  每日限制: ${quotaDetails.freeQuota.dailyLimit} 点数`);
        console.log(`  今日已用: ${quotaDetails.freeQuota.todayUsed} 点数`);
        console.log(`  今日剩余: ${quotaDetails.freeQuota.dailyLimit - quotaDetails.freeQuota.todayUsed} 点数`);
        console.log(`  启用状态: ${quotaDetails.freeQuota.enabled ? '是' : '否'}`);

        // 2. 限制套餐
        console.log('\n💎 限制套餐额度 (优先级2):');
        if (quotaDetails.limitedPackages.length > 0) {
          quotaDetails.limitedPackages.forEach((pkg, index) => {
            console.log(`  套餐${index + 1}: ${pkg.title}`);
            console.log(`    每日限制: ${pkg.dailyLimit} 点数`);
            console.log(`    今日已用: ${pkg.todayUsed} 点数`);
            console.log(`    今日剩余: ${pkg.dailyLimit - pkg.todayUsed} 点数`);
            console.log(`    到期时间: ${new Date(pkg.expiredAt).toLocaleString()}`);
          });
        } else {
          console.log('  无限制套餐');
        }

        // 3. 无限制套餐
        console.log('\n🚀 无限制套餐额度 (优先级3):');
        if (quotaDetails.unlimitedPackages.length > 0) {
          quotaDetails.unlimitedPackages.forEach((pkg, index) => {
            console.log(`  套餐${index + 1}: ${pkg.title}`);
            console.log(`    使用状态: 无限制`);
            console.log(`    今日已用: ${pkg.todayUsed} 点数`);
            console.log(`    到期时间: ${new Date(pkg.expiredAt).toLocaleString()}`);
          });
        } else {
          console.log('  无无限制套餐');
        }

        // 4. 总使用统计
        console.log('\n📈 总使用统计:');
        console.log(`  今日总计: ${quotaDetails.totalUsage.today} 点数`);
        console.log(`  聊天使用: ${quotaDetails.totalUsage.chat} 点数`);
        console.log(`  工作流使用: ${quotaDetails.totalUsage.workflow} 点数`);

        // 5. 使用优先级说明
        console.log('\n💡 使用优先级:');
        console.log('  1. 优先使用免费额度');
        console.log('  2. 其次使用限制套餐额度');
        console.log('  3. 最后使用无限制套餐额度');

      } catch (error) {
        console.error(`❌ 获取用户 ${user.id} 分类额度详情失败:`, error);
      }
    }

    console.log('\n=== 测试API端点 ===');
    console.log('✅ GET /api/daily-limit/quota-details - 获取分类额度详情');
    console.log('✅ GET /api/daily-limit/stats - 获取使用统计 (向后兼容)');
    console.log('✅ POST /api/daily-limit/check - 检查每日限制');

    console.log('\n=== 前端集成 ===');
    console.log('✅ quota-details-modal.vue - 已更新为三类额度显示');
    console.log('✅ 优先级提示 - 已添加使用优先级说明');
    console.log('✅ 分类显示 - 免费额度、限制套餐、无限制套餐分开显示');
    console.log('✅ 总点数余额 - 独立显示用户总点数');

    console.log('\n✅ 分类额度功能测试完成！');

    console.log('\n📋 功能特性总结:');
    console.log('1. 三类额度分离: 免费额度、限制套餐额度、无限制套餐额度');
    console.log('2. 使用优先级: 免费 → 限制套餐 → 无限制套餐');
    console.log('3. 独立统计: 每类额度的使用情况独立计算');
    console.log('4. 清晰展示: 用户可以清楚看到各类额度的使用情况');
    console.log('5. 总点数余额: 独立显示用户的总点数余额');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    // 关闭数据库连接
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('\n🔌 数据库连接已关闭');
    }
  }
}

// 运行测试
testQuotaCategories().catch(console.error);
