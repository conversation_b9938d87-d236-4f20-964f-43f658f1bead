# 配置功能测试清单

## 测试目标
验证所有配置卡片的保存、读取和重置功能是否正常工作

## 测试环境要求
- 后端服务器正常运行
- 数据库连接正常
- 所有新配置表已创建并迁移数据

## 配置类型测试清单

### 1. 微信公众号配置 (wechat-official)
- [ ] **读取功能**: 打开配置页面，验证能正确加载现有配置
- [ ] **保存功能**: 修改配置项并保存，验证保存成功提示
- [ ] **重置功能**: 点击重置按钮，验证配置被清空
- [ ] **字段验证**: 验证必填字段验证规则
- [ ] **API调用**: 检查网络请求是否使用正确的API路径 `/api/config/type/wechat-official`

### 2. 微信小程序配置 (wechat-miniprogram)
- [ ] **读取功能**: 打开配置页面，验证能正确加载现有配置
- [ ] **保存功能**: 修改配置项并保存，验证保存成功提示
- [ ] **重置功能**: 点击重置按钮，验证配置被清空
- [ ] **广告配置**: 验证广告相关配置项的启用/禁用逻辑
- [ ] **API调用**: 检查网络请求是否使用正确的API路径

### 3. 微信支付配置 (wechat-pay)
- [ ] **读取功能**: 打开配置页面，验证能正确加载现有配置
- [ ] **保存功能**: 修改配置项并保存，验证保存成功提示
- [ ] **重置功能**: 点击重置按钮，验证配置被清空
- [ ] **证书上传**: 验证证书文件上传功能
- [ ] **沙箱模式**: 验证沙箱模式开关功能

### 4. 支付宝配置 (alipay)
- [ ] **读取功能**: 打开配置页面，验证能正确加载现有配置
- [ ] **保存功能**: 修改配置项并保存，验证保存成功提示
- [ ] **重置功能**: 点击重置按钮，验证配置被清空
- [ ] **沙箱模式**: 验证沙箱模式开关功能
- [ ] **字段映射**: 验证 `sandboxMode` 字段正确映射

### 5. 扣子配置 (kouzi)
- [ ] **读取功能**: 打开配置页面，验证能正确加载现有配置
- [ ] **保存功能**: 修改配置项并保存，验证保存成功提示
- [ ] **重置功能**: 点击重置按钮，验证配置被清空
- [ ] **认证模式**: 验证认证模式选择功能
- [ ] **字段验证**: 确认不包含已移除的 `appSecret` 字段

### 6. 验证配置 (verification)
- [ ] **读取功能**: 打开配置页面，验证能正确加载现有配置
- [ ] **保存功能**: 修改配置项并保存，验证保存成功提示
- [ ] **重置功能**: 点击重置按钮，验证配置被清空
- [ ] **SMS配置**: 验证短信服务配置项
- [ ] **邮件配置**: 验证邮件服务配置项
- [ ] **存储配置**: 验证对象存储配置项
- [ ] **字段映射**: 验证所有字段名称正确映射（如 `smsAliyunAccessKeyId`）

### 7. 免费额度配置 (free-quota)
- [ ] **读取功能**: 打开配置页面，验证能正确加载现有配置
- [ ] **保存功能**: 修改配置项并保存，验证保存成功提示
- [ ] **重置功能**: 点击重置按钮，验证配置被清空
- [ ] **数值验证**: 验证数值类型字段的输入验证
- [ ] **字段映射**: 验证新字段名称（如 `newUserQuota`, `quotaValidDays`）

### 8. 推荐奖励配置 (referral)
- [ ] **读取功能**: 打开配置页面，验证能正确加载现有配置
- [ ] **保存功能**: 修改配置项并保存，验证保存成功提示
- [ ] **重置功能**: 点击重置按钮，验证配置被清空
- [ ] **海报配置**: 验证海报相关配置项
- [ ] **奖励配置**: 验证奖励点数配置项
- [ ] **合伙人配置**: 验证合伙人相关配置项

### 9. 系统配置 (system)
- [ ] **读取功能**: 打开配置页面，验证能正确加载现有配置
- [ ] **保存功能**: 修改配置项并保存，验证保存成功提示
- [ ] **重置功能**: 点击重置按钮，验证配置被清空
- [ ] **基础配置**: 验证站点名称、描述等基础配置
- [ ] **安全配置**: 验证登录相关安全配置
- [ ] **主题配置**: 验证主题色配置

## 通用功能测试

### API兼容性测试
- [ ] **路径正确性**: 所有配置都使用 `/api/config/type/{configType}` 路径
- [ ] **请求格式**: POST请求发送正确的JSON数据格式
- [ ] **响应处理**: 正确处理API响应和错误情况
- [ ] **本地存储**: 验证本地存储备份机制

### 用户体验测试
- [ ] **加载状态**: 验证加载、保存、重置时的状态提示
- [ ] **错误处理**: 验证网络错误时的降级处理
- [ ] **成功提示**: 验证操作成功时的用户反馈
- [ ] **表单验证**: 验证必填字段和格式验证

### 数据一致性测试
- [ ] **数据库同步**: 验证前端保存的数据正确写入数据库
- [ ] **字段映射**: 验证前端字段名与数据库字段名正确映射
- [ ] **类型转换**: 验证布尔值、数值等类型正确转换
- [ ] **默认值**: 验证新配置的默认值设置

## 测试步骤

### 准备工作
1. 确保后端服务器运行在 `http://localhost:3001`
2. 确保数据库连接正常，所有新配置表已创建
3. 打开浏览器开发者工具，监控网络请求

### 执行测试
1. 逐一打开每个配置页面
2. 检查配置数据是否正确加载
3. 修改配置项并保存，观察网络请求和响应
4. 验证保存成功后重新加载页面，确认数据持久化
5. 测试重置功能，确认配置被正确清空

### 验证结果
1. 检查数据库中对应表的数据是否正确更新
2. 验证所有操作都有适当的用户反馈
3. 确认没有JavaScript错误或网络请求失败

## 已知问题和解决方案

### 字段名称映射问题
- **支付宝配置**: `sandbox` → `sandboxMode`
- **扣子配置**: 移除了 `appSecret` 字段
- **验证配置**: 所有字段使用前缀命名（如 `smsAliyunAccessKeyId`）

### API路径统一
- 所有配置都使用统一的API路径格式
- 前端组件已更新为使用正确的API调用

### 数据库表结构
- 所有新配置表已创建并迁移数据
- 字段类型和约束已正确设置

## 测试完成标准
- [ ] 所有9种配置类型的读取、保存、重置功能正常
- [ ] 所有API调用使用正确的路径和数据格式
- [ ] 数据库中的数据与前端显示一致
- [ ] 用户操作有适当的反馈和错误处理
- [ ] 没有JavaScript错误或网络请求失败
