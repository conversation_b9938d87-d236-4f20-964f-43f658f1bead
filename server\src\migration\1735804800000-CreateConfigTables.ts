import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateConfigTables1735804800000 implements MigrationInterface {
    name = 'CreateConfigTables1735804800000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // 1. 创建微信公众号配置表
        await queryRunner.query(`
            CREATE TABLE IF NOT EXISTS \`config_wechat_official\` (
                \`id\` int(11) NOT NULL AUTO_INCREMENT,
                \`enabled\` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否启用',
                \`app_id\` varchar(100) NOT NULL DEFAULT '' COMMENT '微信公众号AppID',
                \`app_secret\` varchar(255) NOT NULL DEFAULT '' COMMENT '微信公众号AppSecret',
                \`token\` varchar(100) NOT NULL DEFAULT '' COMMENT '微信公众号Token',
                \`encoding_aes_key\` varchar(255) NOT NULL DEFAULT '' COMMENT '消息加解密密钥',
                \`verify_file_name\` varchar(255) NOT NULL DEFAULT '' COMMENT '域名验证文件名',
                \`verify_file_content\` text COMMENT '域名验证文件内容',
                \`created_at\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                \`updated_at\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                PRIMARY KEY (\`id\`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='微信公众号配置表'
        `);

        // 2. 创建微信小程序配置表
        await queryRunner.query(`
            CREATE TABLE IF NOT EXISTS \`config_wechat_miniprogram\` (
                \`id\` int(11) NOT NULL AUTO_INCREMENT,
                \`enabled\` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否启用',
                \`app_id\` varchar(100) NOT NULL DEFAULT '' COMMENT '微信小程序AppID',
                \`app_secret\` varchar(255) NOT NULL DEFAULT '' COMMENT '微信小程序AppSecret',
                \`verify_file_name\` varchar(255) NOT NULL DEFAULT '' COMMENT '域名验证文件名',
                \`verify_file_content\` text COMMENT '域名验证文件内容',
                \`banner_ad_enabled\` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否启用激励视频广告',
                \`banner_ad_id\` varchar(100) NOT NULL DEFAULT '' COMMENT '激励视频广告ID',
                \`interstitial_ad_enabled\` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否启用插屏广告',
                \`interstitial_ad_id\` varchar(100) NOT NULL DEFAULT '' COMMENT '插屏广告ID',
                \`interstitial_ad_count\` int NOT NULL DEFAULT 0 COMMENT '插屏广告次数',
                \`interstitial_ad_interval\` int NOT NULL DEFAULT 0 COMMENT '插屏广告重播间隔',
                \`video_ad_enabled\` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否启用视频广告',
                \`video_ad_id\` varchar(100) NOT NULL DEFAULT '' COMMENT '视频广告ID',
                \`created_at\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                \`updated_at\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                PRIMARY KEY (\`id\`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='微信小程序配置表'
        `);

        // 3. 创建微信支付配置表
        await queryRunner.query(`
            CREATE TABLE IF NOT EXISTS \`config_wechat_pay\` (
                \`id\` int(11) NOT NULL AUTO_INCREMENT,
                \`enabled\` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否启用',
                \`sandbox\` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否为沙箱环境',
                \`mch_id\` varchar(50) NOT NULL DEFAULT '' COMMENT '微信支付商户号',
                \`api_v3_key\` varchar(255) NOT NULL DEFAULT '' COMMENT 'API v3密钥',
                \`api_key\` varchar(255) NOT NULL DEFAULT '' COMMENT 'API v2密钥',
                \`serial_no\` varchar(100) NOT NULL DEFAULT '' COMMENT '证书序列号',
                \`certificate_content\` text COMMENT '证书内容',
                \`private_key_content\` text COMMENT '私钥内容',
                \`notify_url\` varchar(255) NOT NULL DEFAULT '' COMMENT '支付结果通知URL',
                \`created_at\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                \`updated_at\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                PRIMARY KEY (\`id\`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='微信支付配置表'
        `);

        // 4. 创建支付宝配置表
        await queryRunner.query(`
            CREATE TABLE IF NOT EXISTS \`config_alipay\` (
                \`id\` int(11) NOT NULL AUTO_INCREMENT,
                \`enabled\` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否启用',
                \`sandbox_mode\` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否为沙箱模式',
                \`app_id\` varchar(50) NOT NULL DEFAULT '' COMMENT '支付宝应用ID',
                \`private_key\` text COMMENT '应用私钥',
                \`alipay_public_key\` text COMMENT '支付宝公钥',
                \`sign_type\` varchar(10) NOT NULL DEFAULT 'RSA2' COMMENT '签名类型',
                \`notify_url\` varchar(255) NOT NULL DEFAULT '' COMMENT '支付结果通知URL',
                \`enable_pc_pay\` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用电脑网站支付',
                \`pc_return_url\` varchar(255) NOT NULL DEFAULT '' COMMENT '电脑网站支付同步返回地址',
                \`enable_wap_pay\` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用手机网站支付',
                \`wap_return_url\` varchar(255) NOT NULL DEFAULT '' COMMENT '手机网站支付同步返回地址',
                \`wap_quit_url\` varchar(255) NOT NULL DEFAULT '' COMMENT '手机网站支付中途退出返回地址',
                \`created_at\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                \`updated_at\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                PRIMARY KEY (\`id\`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='支付宝配置表'
        `);

        // 5. 创建扣子配置表
        await queryRunner.query(`
            CREATE TABLE IF NOT EXISTS \`config_kouzi\` (
                \`id\` int(11) NOT NULL AUTO_INCREMENT,
                \`enabled\` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否启用',
                \`app_id\` varchar(100) NOT NULL DEFAULT '' COMMENT '扣子OAuth应用ID',
                \`auth_mode\` varchar(20) NOT NULL DEFAULT 'jwt' COMMENT '认证方式',
                \`private_key\` text COMMENT '扣子OAuth应用私钥',
                \`public_key\` text COMMENT '扣子OAuth应用公钥',
                \`personal_access_token\` varchar(255) NOT NULL DEFAULT '' COMMENT '个人访问令牌（备用）',
                \`created_at\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                \`updated_at\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                PRIMARY KEY (\`id\`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='扣子配置表'
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE IF EXISTS \`config_kouzi\``);
        await queryRunner.query(`DROP TABLE IF EXISTS \`config_alipay\``);
        await queryRunner.query(`DROP TABLE IF EXISTS \`config_wechat_pay\``);
        await queryRunner.query(`DROP TABLE IF EXISTS \`config_wechat_miniprogram\``);
        await queryRunner.query(`DROP TABLE IF EXISTS \`config_wechat_official\``);
    }
}
