import { MigrationInterface, QueryRunner } from "typeorm";

export class AddShareImageUrlToConfigReferral1735900000000 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        // 添加分享图片URL字段到推荐配置表
        await queryRunner.query(`
            ALTER TABLE \`config_referral\` 
            ADD COLUMN \`share_image_url\` varchar(500) NOT NULL DEFAULT '' COMMENT '分享图片URL' 
            AFTER \`share_description\`
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // 删除分享图片URL字段
        await queryRunner.query(`
            ALTER TABLE \`config_referral\` 
            DROP COLUMN \`share_image_url\`
        `);
    }
}
