/**
 * 验证配置实体类（短信、邮箱、对象存储）
 */
import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('config_verification')
export class ConfigVerification {
  @PrimaryGeneratedColumn()
  id: number;

  // 短信验证配置
  @Column({ name: 'sms_provider', length: 20, default: 'aliyun', comment: '短信服务提供商' })
  smsProvider: string;

  @Column({ name: 'sms_enabled', type: 'tinyint', width: 1, default: 0, comment: '是否启用短信验证' })
  smsEnabled: boolean;

  // 阿里云短信配置
  @Column({ name: 'sms_aliyun_access_key_id', length: 100, default: '', comment: '阿里云短信Access Key ID' })
  smsAliyunAccessKeyId: string;

  @Column({ name: 'sms_aliyun_access_key_secret', length: 255, default: '', comment: '阿里云短信Access Key Secret' })
  smsAliyunAccessKeySecret: string;

  @Column({ name: 'sms_aliyun_sign_name', length: 50, default: '', comment: '阿里云短信签名' })
  smsAliyunSignName: string;

  @Column({ name: 'sms_aliyun_template_code', length: 50, default: '', comment: '阿里云短信验证码模板代码' })
  smsAliyunTemplateCode: string;

  // 腾讯云短信配置
  @Column({ name: 'sms_tencent_secret_id', length: 100, default: '', comment: '腾讯云短信Secret ID' })
  smsTencentSecretId: string;

  @Column({ name: 'sms_tencent_secret_key', length: 255, default: '', comment: '腾讯云短信Secret Key' })
  smsTencentSecretKey: string;

  @Column({ name: 'sms_tencent_app_id', length: 50, default: '', comment: '腾讯云短信应用ID' })
  smsTencentAppId: string;

  @Column({ name: 'sms_tencent_sign_name', length: 50, default: '', comment: '腾讯云短信签名' })
  smsTencentSignName: string;

  @Column({ name: 'sms_tencent_template_id', length: 50, default: '', comment: '腾讯云短信验证码模板ID' })
  smsTencentTemplateId: string;

  // 华为云短信配置
  @Column({ name: 'sms_huawei_app_key', length: 100, default: '', comment: '华为云短信App Key' })
  smsHuaweiAppKey: string;

  @Column({ name: 'sms_huawei_app_secret', length: 255, default: '', comment: '华为云短信App Secret' })
  smsHuaweiAppSecret: string;

  @Column({ name: 'sms_huawei_sign_name', length: 50, default: '', comment: '华为云短信签名' })
  smsHuaweiSignName: string;

  @Column({ name: 'sms_huawei_template_id', length: 50, default: '', comment: '华为云短信验证码模板ID' })
  smsHuaweiTemplateId: string;

  // 邮箱验证配置
  @Column({ name: 'email_provider', length: 20, default: 'smtp', comment: '邮件服务提供商' })
  emailProvider: string;

  @Column({ name: 'email_enabled', type: 'tinyint', width: 1, default: 0, comment: '是否启用邮箱验证' })
  emailEnabled: boolean;

  // SMTP邮箱配置
  @Column({ name: 'email_smtp_host', length: 100, default: '', comment: 'SMTP服务器地址' })
  emailSmtpHost: string;

  @Column({ name: 'email_smtp_port', type: 'int', default: 587, comment: 'SMTP服务器端口' })
  emailSmtpPort: number;

  @Column({ name: 'email_smtp_from_email', length: 100, default: '', comment: 'SMTP发件人邮箱' })
  emailSmtpFromEmail: string;

  @Column({ name: 'email_smtp_password', length: 255, default: '', comment: 'SMTP邮箱密码或授权码' })
  emailSmtpPassword: string;

  @Column({ name: 'email_smtp_from_name', length: 100, default: '', comment: 'SMTP发件人名称' })
  emailSmtpFromName: string;

  @Column({ name: 'email_smtp_ssl', type: 'tinyint', width: 1, default: 1, comment: 'SMTP是否启用SSL' })
  emailSmtpSsl: boolean;

  // 阿里云邮件推送配置
  @Column({ name: 'email_aliyun_access_key_id', length: 100, default: '', comment: '阿里云邮件推送Access Key ID' })
  emailAliyunAccessKeyId: string;

  @Column({ name: 'email_aliyun_access_key_secret', length: 255, default: '', comment: '阿里云邮件推送Access Key Secret' })
  emailAliyunAccessKeySecret: string;

  @Column({ name: 'email_aliyun_from_email', length: 100, default: '', comment: '阿里云邮件推送发件人邮箱' })
  emailAliyunFromEmail: string;

  @Column({ name: 'email_aliyun_from_name', length: 100, default: '', comment: '阿里云邮件推送发件人名称' })
  emailAliyunFromName: string;

  // 腾讯云邮件推送配置
  @Column({ name: 'email_tencent_secret_id', length: 100, default: '', comment: '腾讯云邮件推送Secret ID' })
  emailTencentSecretId: string;

  @Column({ name: 'email_tencent_secret_key', length: 255, default: '', comment: '腾讯云邮件推送Secret Key' })
  emailTencentSecretKey: string;

  @Column({ name: 'email_tencent_from_email', length: 100, default: '', comment: '腾讯云邮件推送发件人邮箱' })
  emailTencentFromEmail: string;

  @Column({ name: 'email_tencent_from_name', length: 100, default: '', comment: '腾讯云邮件推送发件人名称' })
  emailTencentFromName: string;

  // 对象存储配置
  @Column({ name: 'storage_provider', length: 20, default: 'aliyun', comment: '对象存储服务提供商' })
  storageProvider: string;

  @Column({ name: 'storage_enabled', type: 'tinyint', width: 1, default: 0, comment: '是否启用对象存储' })
  storageEnabled: boolean;

  // 阿里云OSS配置
  @Column({ name: 'storage_aliyun_access_key_id', length: 100, default: '', comment: '阿里云OSS Access Key ID' })
  storageAliyunAccessKeyId: string;

  @Column({ name: 'storage_aliyun_access_key_secret', length: 255, default: '', comment: '阿里云OSS Access Key Secret' })
  storageAliyunAccessKeySecret: string;

  @Column({ name: 'storage_aliyun_bucket', length: 100, default: '', comment: '阿里云OSS Bucket名称' })
  storageAliyunBucket: string;

  @Column({ name: 'storage_aliyun_endpoint', length: 255, default: '', comment: '阿里云OSS访问域名' })
  storageAliyunEndpoint: string;

  @Column({ name: 'storage_aliyun_base_url', length: 255, default: '', comment: '阿里云OSS基础URL' })
  storageAliyunBaseUrl: string;

  // 腾讯云COS配置
  @Column({ name: 'storage_tencent_secret_id', length: 100, default: '', comment: '腾讯云COS Secret ID' })
  storageTencentSecretId: string;

  @Column({ name: 'storage_tencent_secret_key', length: 255, default: '', comment: '腾讯云COS Secret Key' })
  storageTencentSecretKey: string;

  @Column({ name: 'storage_tencent_bucket', length: 100, default: '', comment: '腾讯云COS Bucket名称' })
  storageTencentBucket: string;

  @Column({ name: 'storage_tencent_base_url', length: 255, default: '', comment: '腾讯云COS基础URL' })
  storageTencentBaseUrl: string;

  @CreateDateColumn({ name: 'created_at', comment: '创建时间' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', comment: '更新时间' })
  updatedAt: Date;
}
