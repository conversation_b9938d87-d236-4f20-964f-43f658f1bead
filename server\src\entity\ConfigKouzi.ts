/**
 * 扣子配置实体类
 */
import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('config_kouzi')
export class ConfigKouzi {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'tinyint', width: 1, default: 0, comment: '是否启用' })
  enabled: boolean;

  @Column({ name: 'app_id', length: 100, default: '', comment: '扣子OAuth应用ID' })
  appId: string;

  @Column({ name: 'auth_mode', length: 20, default: 'jwt', comment: '认证方式' })
  authMode: string;

  @Column({ name: 'private_key', type: 'text', nullable: true, comment: '扣子OAuth应用私钥' })
  privateKey: string;

  @Column({ name: 'public_key', type: 'text', nullable: true, comment: '扣子OAuth应用公钥' })
  publicKey: string;

  @Column({ name: 'personal_access_token', length: 255, default: '', comment: '个人访问令牌（备用）' })
  personalAccessToken: string;

  @CreateDateColumn({ name: 'created_at', comment: '创建时间' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', comment: '更新时间' })
  updatedAt: Date;
}
