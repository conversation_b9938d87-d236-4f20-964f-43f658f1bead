import { API_BASE_URL } from '@/config/index.js'
import { userStore } from './members.js'

/**
 * 分享API
 */
class ShareApi {
  constructor() {
    this.baseURL = API_BASE_URL
  }

  /**
   * 封装uni.request为Promise，自动添加用户认证
   */
  request(options) {
    return new Promise((resolve, reject) => {
      // 构建完整的URL
      let fullUrl = options.url
      if (!fullUrl.startsWith('http')) {
        fullUrl = this.baseURL + fullUrl
      }

      // 获取用户token
      const token = userStore.getToken()

      // 构建请求头
      const headers = {
        'Content-Type': 'application/json',
        ...options.header
      }

      // 如果有token，添加Authorization头
      if (token) {
        headers.Authorization = `Bearer ${token}`
      }

      uni.request({
        ...options,
        url: fullUrl,
        header: headers,
        success: (res) => {
          resolve(res)
        },
        fail: (err) => {
          reject(err)
        }
      })
    })
  }

  /**
   * 生成分享邀请码
   * @param {Object} params - 分享参数
   * @param {string} params.type - 分享类型 (agent|workflow)
   * @param {string} params.targetId - 目标ID (智能体ID或工作流ID)
   * @returns {Promise} 邀请码信息
   */
  async generateInviteCode(params) {
    try {
      console.log('生成邀请码:', params)

      const response = await this.request({
        url: '/api/share/generate-invite-code',
        method: 'POST',
        data: params
      })

      if (response.statusCode === 200) {
        console.log('邀请码生成成功:', response.data)
        return response.data
      } else {
        throw new Error(`生成邀请码失败: ${response.statusCode}`)
      }
    } catch (error) {
      console.error('生成邀请码失败:', error)
      throw error
    }
  }

  /**
   * 获取分享配置
   * @returns {Promise} 分享配置信息
   */
  async getShareConfig() {
    try {
      const response = await this.request({
        url: '/api/config/referral',
        method: 'GET'
      })

      if (response.statusCode === 200 && response.data.success) {
        console.log('获取分享配置成功:', response.data)
        return {
          success: true,
          data: response.data.data || {}
        }
      } else {
        throw new Error(`获取分享配置失败: ${response.statusCode}`)
      }
    } catch (error) {
      console.error('获取分享配置失败:', error)
      throw error
    }
  }

  /**
   * 微信小程序分享
   * @param {Object} params - 分享参数
   * @param {string} params.title - 分享标题
   * @param {string} params.desc - 分享描述
   * @param {string} params.imageUrl - 分享图片
   * @param {string} params.path - 分享路径
   * @returns {Object} 分享配置
   */
  getWechatShareConfig(params) {
    const { title, desc, imageUrl, path } = params

    return {
      title: title || 'AI智能体使用邀请',
      desc: desc || '注册即可获得额外免费使用次数，快来体验智能AI助手！',
      imageUrl: imageUrl || '',
      path: path || '/pages/index/index'
    }
  }

  /**
   * 处理邀请码访问
   * @param {string} inviteCode - 邀请码
   * @returns {Promise} 处理结果
   */
  async handleInviteCode(inviteCode) {
    try {
      console.log('处理邀请码:', inviteCode)

      const response = await this.request({
        url: '/api/share/handle-invite-code',
        method: 'POST',
        data: { inviteCode }
      })

      if (response.statusCode === 200) {
        console.log('邀请码处理成功:', response.data)
        return response.data
      } else {
        throw new Error(`邀请码处理失败: ${response.statusCode}`)
      }
    } catch (error) {
      console.error('邀请码处理失败:', error)
      throw error
    }
  }
}

// 创建实例
const shareApi = new ShareApi()

export default shareApi
