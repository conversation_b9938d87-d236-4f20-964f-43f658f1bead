/**
 * 微信小程序配置实体类
 */
import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('config_wechat_miniprogram')
export class ConfigWechatMiniprogram {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'tinyint', width: 1, default: 0, comment: '是否启用' })
  enabled: boolean;

  @Column({ name: 'app_id', length: 100, default: '', comment: '微信小程序AppID' })
  appId: string;

  @Column({ name: 'app_secret', length: 255, default: '', comment: '微信小程序AppSecret' })
  appSecret: string;

  @Column({ name: 'verify_file_name', length: 255, default: '', comment: '域名验证文件名' })
  verifyFileName: string;

  @Column({ name: 'verify_file_content', type: 'text', comment: '域名验证文件内容' })
  verifyFileContent: string;

  // 广告配置
  @Column({ name: 'banner_ad_enabled', type: 'tinyint', width: 1, default: 0, comment: '是否启用激励视频广告' })
  bannerAdEnabled: boolean;

  @Column({ name: 'banner_ad_id', length: 100, default: '', comment: '激励视频广告ID' })
  bannerAdId: string;

  @Column({ name: 'interstitial_ad_enabled', type: 'tinyint', width: 1, default: 0, comment: '是否启用插屏广告' })
  interstitialAdEnabled: boolean;

  @Column({ name: 'interstitial_ad_id', length: 100, default: '', comment: '插屏广告ID' })
  interstitialAdId: string;

  @Column({ name: 'interstitial_ad_count', type: 'int', default: 0, comment: '插屏广告次数' })
  interstitialAdCount: number;

  @Column({ name: 'interstitial_ad_interval', type: 'int', default: 0, comment: '插屏广告重播间隔' })
  interstitialAdInterval: number;

  @Column({ name: 'video_ad_enabled', type: 'tinyint', width: 1, default: 0, comment: '是否启用视频广告' })
  videoAdEnabled: boolean;

  @Column({ name: 'video_ad_id', length: 100, default: '', comment: '视频广告ID' })
  videoAdId: string;

  @CreateDateColumn({ name: 'created_at', comment: '创建时间' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', comment: '更新时间' })
  updatedAt: Date;
}
