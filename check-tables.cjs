const mysql = require('mysql2/promise');

async function checkTables() {
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'root',
      database: 'ai_agent'
    });

    console.log('连接到数据库成功');

    // 检查配置相关的表
    const [tables] = await connection.execute("SHOW TABLES LIKE 'config_%'");
    console.log('配置表列表:');
    console.log(tables);

    // 检查系统配置表是否存在
    const [systemTables] = await connection.execute("SHOW TABLES LIKE 'config_system'");
    if (systemTables.length > 0) {
      console.log('\nconfig_system表存在，查看表结构:');
      const [structure] = await connection.execute("DESCRIBE config_system");
      console.log(structure);
      
      console.log('\n查看config_system表数据:');
      const [data] = await connection.execute("SELECT * FROM config_system");
      console.log(data);
    } else {
      console.log('\nconfig_system表不存在');
    }

    // 检查扣子配置表
    const [kouziTables] = await connection.execute("SHOW TABLES LIKE 'config_kouzi'");
    if (kouziTables.length > 0) {
      console.log('\nconfig_kouzi表存在，查看数据:');
      const [kouziData] = await connection.execute("SELECT * FROM config_kouzi");
      console.log(kouziData);
    } else {
      console.log('\nconfig_kouzi表不存在');
    }

    await connection.end();
  } catch (error) {
    console.error('检查表失败:', error);
  }
}

checkTables();
