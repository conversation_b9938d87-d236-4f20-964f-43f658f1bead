<template>
	<view v-if="visible" class="quota-modal-overlay" @click="handleOverlayClick">
		<!-- 自定义提示组件 -->
		<view v-if="showCustomToast" class="custom-toast" :class="{ 'toast-error': toastType === 'error' }">
			<text class="toast-text">{{ toastMessage }}</text>
		</view>

		<view class="quota-modal-container" @click.stop>
			<!-- 弹窗头部 -->
			<view class="quota-modal-header">
				<text class="quota-modal-title">额度详情</text>
				<view class="quota-close-btn" @click="closeModal">
					<text class="close-icon">✕</text>
				</view>
			</view>

			<!-- 弹窗内容 -->
			<view class="quota-modal-content">
				<!-- 当前点数显示 -->
				<view class="current-points-section">
					<view class="points-display">
						<text class="points-number">{{ userInfo.points || 0 }}</text>
						<text class="points-unit">点数</text>
					</view>
					<text class="points-subtitle">当前可用点数</text>
				</view>

				<!-- 额度信息 -->
				<view class="quota-simple">
					<!-- 额度列表 -->
					<view class="quota-list">
						<!-- 免费额度 -->
						<view class="quota-item-simple">
							<view class="quota-info">
								<text class="quota-name">🎁 免费额度</text>
								<view class="quota-details">
									<text class="quota-total">总额度: {{ quotaDetails.freeQuota.totalPoints || 0 }} 点数</text>
									<text class="quota-remaining">今日剩余: {{ Math.max(0, (quotaDetails.freeQuota.dailyLimit || 20) - (quotaDetails.freeQuota.todayUsed || 0)) }} 点数</text>
									<text class="quota-limit">日限额: {{ quotaDetails.freeQuota.dailyLimit || 20 }} 点数</text>
								</view>
							</view>
						</view>

						<!-- 限制套餐额度 -->
						<view v-for="(pkg, index) in quotaDetails.limitedPackages" :key="index" class="quota-item-simple">
							<view class="quota-info">
								<text class="quota-name">💎 {{ pkg.title }}</text>
								<view class="quota-details">
									<text class="quota-total">总购买: {{ pkg.totalPoints || 0 }} 点数</text>
									<text class="quota-remaining">今日剩余: {{ Math.max(0, pkg.dailyLimit - (pkg.todayUsed || 0)) }} 点数</text>
									<text class="quota-limit">日限额: {{ pkg.dailyLimit }} 点数</text>
								</view>
							</view>
						</view>

						<!-- 无限制套餐额度 -->
						<view v-for="(pkg, index) in quotaDetails.unlimitedPackages" :key="index" class="quota-item-simple">
							<view class="quota-info">
								<text class="quota-name">🚀 {{ pkg.title }}</text>
								<view class="quota-details">
									<text class="quota-total">总购买: {{ pkg.totalPoints || 0 }} 点数</text>
									<text class="quota-remaining">今日已用: {{ pkg.todayUsed || 0 }} 点数</text>
									<text class="quota-limit">剩余: 无限制</text>
								</view>
							</view>
						</view>

						<!-- 无套餐提示 -->
						<view v-if="quotaDetails.limitedPackages.length === 0 && quotaDetails.unlimitedPackages.length === 0" class="quota-item-simple">
							<view class="quota-info">
								<text class="quota-name">📦 套餐额度</text>
								<view class="quota-details">
									<text class="quota-limit">暂无有效套餐</text>
								</view>
							</view>
						</view>
					</view>
				</view>

				<!-- 操作按钮 -->
				<view class="quota-actions">
					<view v-if="quotaDetails.limitedPackages.length === 0 && quotaDetails.unlimitedPackages.length === 0" class="action-btn primary" @click="navigateToPackages">
						<text class="btn-text">购买套餐</text>
					</view>
					<view class="action-btn secondary" @click="closeModal">
						<text class="btn-text">关闭</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { API_BASE_URL } from '@/config/index.js'

export default {
	name: 'QuotaDetailsModal',
	props: {
		visible: {
			type: Boolean,
			default: false
		},
		userInfo: {
			type: Object,
			default: () => ({})
		}
	},
	data() {
		return {
			// 额度详情 - 分三类显示
			quotaDetails: {
				// 免费额度 (优先级1)
				freeQuota: {
					dailyLimit: 20,
					todayUsed: 0,
					enabled: true
				},
				// 限制套餐列表 (优先级2)
				limitedPackages: [],
				// 无限制套餐列表 (优先级3)
				unlimitedPackages: [],
				// 总使用统计
				totalUsage: {
					today: 0,
					chat: 0,
					workflow: 0
				}
			},
			// 自定义提示相关
			showCustomToast: false,
			toastMessage: '',
			toastType: 'success' // 'success' 或 'error'
		}
	},
	watch: {
		visible(newVal) {
			if (newVal) {
				this.fetchQuotaDetails()
			}
		}
	},
	methods: {
		// 显示自定义提示
		showToast(message, type = 'success') {
			this.toastMessage = message;
			this.toastType = type;
			this.showCustomToast = true;

			// 3秒后自动隐藏
			setTimeout(() => {
				this.showCustomToast = false;
			}, 3000);
		},

		// 获取额度详情
		async fetchQuotaDetails() {
			try {
				const token = uni.getStorageSync('token')
				if (!token) {
					console.warn('未找到token，无法获取额度详情')
					return
				}

				// 获取分类额度详情
				const response = await uni.request({
					url: `${API_BASE_URL}/api/daily-limit/quota-details`,
					method: 'GET',
					header: {
						'Authorization': `Bearer ${token}`,
						'Content-Type': 'application/json'
					}
				})

				console.log('分类额度详情API响应:', response)

				if (response.statusCode === 200 && response.data.code === 200) {
					const data = response.data.data

					// 更新额度详情
					this.quotaDetails = {
						freeQuota: {
							totalPoints: data.freeQuota.totalPoints || 0,
							dailyLimit: data.freeQuota.dailyLimit || 20,
							todayUsed: data.freeQuota.todayUsed || 0,
							remaining: data.freeQuota.remaining || 0,
							enabled: data.freeQuota.enabled || true,
							details: data.freeQuota.details || []
						},
						limitedPackages: (data.limitedPackages || []).map(pkg => ({
							...pkg,
							totalPoints: pkg.totalPoints || 0
						})),
						unlimitedPackages: (data.unlimitedPackages || []).map(pkg => ({
							...pkg,
							totalPoints: pkg.totalPoints || 0
						})),
						totalTodayUsed: data.totalTodayUsed || 0
					}

					console.log('✅ 额度详情更新成功:', this.quotaDetails)
				} else {
					console.error('API返回错误:', response.data.message)
					this.showToast(response.data.message || '获取额度信息失败', 'error')
				}
			} catch (error) {
				console.error('获取额度详情失败:', error)
				this.showToast('获取额度信息失败', 'error')
			}
		},

		// 获取来源描述
		getSourceDescription(type) {
			const descriptions = {
				'register_gift': '注册赠送',
				'invite_gift': '邀请赠送',
				'package_purchase': '套餐购买',
				'redemption_code': '兑换码'
			}
			return descriptions[type] || type
		},

		// 格式化日期
		formatDate(dateStr) {
			if (!dateStr) return '无'

			try {
				const date = new Date(dateStr)
				const year = date.getFullYear()
				const month = String(date.getMonth() + 1).padStart(2, '0')
				const day = String(date.getDate()).padStart(2, '0')
				return `${year}-${month}-${day}`
			} catch (error) {
				return '无效日期'
			}
		},

		// 跳转到套餐页面
		navigateToPackages() {
			this.closeModal()
			try {
				uni.navigateTo({
					url: '/pages/chat/index'
				})
			} catch (error) {
				console.error('跳转到套餐页面失败:', error)
				this.showToast('页面暂未开放', 'error')
			}
		},

		// 处理遮罩层点击
		handleOverlayClick() {
			this.closeModal()
		},

		// 关闭弹窗
		closeModal() {
			this.$emit('close')
		}
	}
}
</script>

<style scoped>
.quota-modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.6);
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 9999;
}

/* 自定义提示样式 */
.custom-toast {
	position: fixed;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	background-color: rgba(0, 0, 0, 0.8);
	color: white;
	padding: 12px 24px;
	border-radius: 8px;
	z-index: 99999; /* 确保在弹窗之上 */
	max-width: 80%;
	text-align: center;
	animation: toast-fade-in 0.3s ease-out;
}

.custom-toast.toast-error {
	background-color: rgba(220, 53, 69, 0.9);
}

.toast-text {
	font-size: 14px;
	line-height: 1.4;
	color: white;
}

@keyframes toast-fade-in {
	from {
		opacity: 0;
		transform: translate(-50%, -50%) scale(0.8);
	}
	to {
		opacity: 1;
		transform: translate(-50%, -50%) scale(1);
	}
}

.quota-modal-container {
	width: 90%;
	max-width: 400px;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 20px;
	overflow: hidden;
	box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.quota-modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20px;
	background: rgba(255, 255, 255, 0.1);
	backdrop-filter: blur(10px);
}

.quota-modal-title {
	font-size: 18px;
	font-weight: bold;
	color: white;
}

.quota-close-btn {
	width: 30px;
	height: 30px;
	border-radius: 15px;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	justify-content: center;
	align-items: center;
}

.close-icon {
	color: white;
	font-size: 16px;
	font-weight: bold;
}

.quota-modal-content {
	padding: 20px;
	background: white;
}

.current-points-section {
	text-align: center;
	margin-bottom: 20px;
	padding: 20px;
	background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
	border-radius: 15px;
	color: white;
}

.points-display {
	display: flex;
	justify-content: center;
	align-items: baseline;
	margin-bottom: 5px;
}

.points-number {
	font-size: 32px;
	font-weight: bold;
	margin-right: 5px;
}

.points-unit {
	font-size: 16px;
	opacity: 0.9;
}

.points-subtitle {
	font-size: 14px;
	opacity: 0.8;
}

/* 简化的额度显示样式 */
.quota-simple {
	margin-bottom: 20px;
}

.quota-list {
	display: flex;
	flex-direction: column;
	gap: 12px;
}

.quota-item-simple {
	background: #fff;
	border-radius: 8px;
	padding: 15px;
	border: 1px solid #e0e0e0;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.quota-info {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
}

.quota-name {
	font-size: 16px;
	font-weight: bold;
	color: #333;
	flex: 1;
}

.quota-details {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	gap: 4px;
}

.quota-remaining {
	font-size: 16px;
	font-weight: bold;
	color: #4caf50;
}

.quota-limit {
	font-size: 12px;
	color: #666;
	font-weight: normal;
}

.quota-card {
	margin-bottom: 15px;
	border-radius: 12px;
	overflow: hidden;
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.free-quota {
	background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.package-quota {
	background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

.no-package {
	background: linear-gradient(135deg, #e0e0e0 0%, #f0f0f0 100%);
}

.card-header {
	display: flex;
	align-items: center;
	padding: 15px;
	background: rgba(255, 255, 255, 0.3);
}

.card-icon {
	font-size: 20px;
	margin-right: 10px;
}

.card-title {
	font-size: 16px;
	font-weight: bold;
	color: #333;
}

.card-content {
	padding: 15px;
}

.quota-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 10px;
	padding: 8px 12px;
	background: rgba(255, 255, 255, 0.5);
	border-radius: 8px;
}

.quota-label {
	font-size: 14px;
	color: #666;
}

.quota-value {
	font-size: 16px;
	font-weight: bold;
	color: #333;
}

.quota-value.used {
	color: #ff6b6b;
}

.quota-value.remaining {
	color: #51cf66;
}

.quota-unit {
	font-size: 12px;
	color: #999;
	margin-left: 4px;
}

.unlimited-notice {
	text-align: center;
	padding: 20px;
}

.unlimited-text {
	display: block;
	font-size: 18px;
	font-weight: bold;
	color: #333;
	margin-bottom: 5px;
}

.unlimited-desc {
	font-size: 14px;
	color: #666;
}

.no-package-notice {
	text-align: center;
	padding: 20px;
}

.no-package-text {
	display: block;
	font-size: 16px;
	font-weight: bold;
	color: #999;
	margin-bottom: 5px;
}

.no-package-desc {
	font-size: 14px;
	color: #bbb;
}

.package-expiry {
	margin-top: 10px;
	padding: 8px 12px;
	background: rgba(255, 255, 255, 0.5);
	border-radius: 8px;
	display: flex;
	justify-content: space-between;
}

.expiry-label {
	font-size: 14px;
	color: #666;
}

.expiry-date {
	font-size: 14px;
	font-weight: bold;
	color: #333;
}

.quota-actions {
	display: flex;
	gap: 10px;
}

.action-btn {
	flex: 1;
	padding: 12px;
	border-radius: 10px;
	text-align: center;
	font-weight: bold;
}

.action-btn.primary {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}

.action-btn.secondary {
	background: #f5f5f5;
	color: #666;
}

.btn-text {
	font-size: 16px;
}
</style>
