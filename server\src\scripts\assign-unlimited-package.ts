import { AppDataSource } from '../data-source'

async function assignUnlimitedPackage() {
  try {
    await AppDataSource.initialize()
    console.log('数据库连接成功')

    const testMemberId = 1
    const unlimitedPackageId = 17 // 测试无限制套餐

    // 1. 检查用户是否存在
    const user = await AppDataSource.query(`
      SELECT id, name, packageId, packageExpiredAt
      FROM members
      WHERE id = ?
    `, [testMemberId])

    if (user.length === 0) {
      console.log('用户不存在，创建测试用户...')
      await AppDataSource.query(`
        INSERT INTO members (id, name, password, email, phone, packageId, packageExpiredAt)
        VALUES (?, 'testuser', 'password123', '<EMAIL>', '13800138999', ?, DATE_ADD(NOW(), INTERVAL 30 DAY))
      `, [testMemberId, unlimitedPackageId])
    } else {
      console.log('用户已存在，更新套餐信息...')
      await AppDataSource.query(`
        UPDATE members
        SET packageId = ?, packageExpiredAt = DATE_ADD(NOW(), INTERVAL 30 DAY)
        WHERE id = ?
      `, [unlimitedPackageId, testMemberId])
    }

    // 2. 获取用户当前点数余额
    const currentUser = await AppDataSource.query(`
      SELECT points FROM members WHERE id = ?
    `, [testMemberId])

    const currentPoints = currentUser[0]?.points || 0
    const addPoints = 10000
    const newPoints = currentPoints + addPoints

    // 3. 更新用户点数
    await AppDataSource.query(`
      UPDATE members SET points = ? WHERE id = ?
    `, [newPoints, testMemberId])

    // 4. 添加套餐购买记录
    await AppDataSource.query(`
      INSERT INTO points_records (memberId, type, amount, balanceBefore, balanceAfter, description, relatedId, createdAt)
      VALUES (?, 'package_purchase', ?, ?, ?, '购买测试无限制套餐', ?, NOW())
    `, [testMemberId, addPoints, currentPoints, newPoints, unlimitedPackageId])

    console.log('✅ 成功为用户分配无限制套餐')

    // 3. 验证结果
    const updatedUser = await AppDataSource.query(`
      SELECT m.id, m.name, m.packageId, m.packageExpiredAt, p.title, p.dailyMaxConsumption
      FROM members m
      LEFT JOIN package p ON m.packageId = p.id
      WHERE m.id = ?
    `, [testMemberId])

    console.log('更新后的用户信息:', updatedUser)

    const pointsRecords = await AppDataSource.query(`
      SELECT type, amount, description, relatedId, createdAt
      FROM points_records
      WHERE memberId = ?
      ORDER BY createdAt DESC
      LIMIT 5
    `, [testMemberId])

    console.log('最近的点数记录:', pointsRecords)

  } catch (error) {
    console.error('操作失败:', error)
  } finally {
    await AppDataSource.destroy()
  }
}

assignUnlimitedPackage()
