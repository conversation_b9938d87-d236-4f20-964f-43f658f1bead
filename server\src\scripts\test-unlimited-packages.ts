import { AppDataSource } from '../data-source'
import { DailyLimitService } from '../services/daily-limit.service'

async function testUnlimitedPackages() {
  try {
    await AppDataSource.initialize()
    console.log('数据库连接成功')

    // 测试用户ID（请根据实际情况修改）
    const testMemberId = 1

    console.log(`\n=== 测试用户 ${testMemberId} 的无限制套餐查询 ===`)

    // 1. 查看用户的套餐信息
    const memberPackageInfo = await AppDataSource.query(`
      SELECT m.id, m.packageId, m.packageExpiredAt, p.title, p.dailyMaxConsumption
      FROM members m
      LEFT JOIN package p ON m.packageId = p.id
      WHERE m.id = ?
    `, [testMemberId])

    console.log('用户套餐信息:', memberPackageInfo)

    // 2. 查看所有套餐
    const allPackages = await AppDataSource.query(`
      SELECT id, title, dailyMaxConsumption, price
      FROM package
      ORDER BY id
    `)

    console.log('\n所有套餐信息:')
    allPackages.forEach((pkg: any) => {
      console.log(`- ID: ${pkg.id}, 标题: ${pkg.title}, 日限额: ${pkg.dailyMaxConsumption}, 价格: ${pkg.price}`)
    })

    // 3. 测试无限制套餐统计
    const unlimitedStats = await DailyLimitService.getUnlimitedPackageStats(testMemberId)
    console.log('\n无限制套餐统计:', unlimitedStats)

    // 4. 测试完整的分类额度详情
    const quotaDetails = await DailyLimitService.getQuotaDetailsByCategory(testMemberId)
    console.log('\n完整的分类额度详情:')
    console.log('- 免费额度:', quotaDetails.freeQuota)
    console.log('- 限制套餐:', quotaDetails.limitedPackages)
    console.log('- 无限制套餐:', quotaDetails.unlimitedPackages)

  } catch (error) {
    console.error('测试失败:', error)
  } finally {
    await AppDataSource.destroy()
  }
}

testUnlimitedPackages()
