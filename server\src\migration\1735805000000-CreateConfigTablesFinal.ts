import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateConfigTablesFinal1735805000000 implements MigrationInterface {
    name = 'CreateConfigTablesFinal1735805000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // 8. 创建推荐奖励配置表
        await queryRunner.query(`
            CREATE TABLE IF NOT EXISTS \`config_referral\` (
                \`id\` int(11) NOT NULL AUTO_INCREMENT,
                \`enabled\` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否启用推荐奖励',
                -- 海报配置
                \`poster_background_image\` varchar(500) NOT NULL DEFAULT '' COMMENT '海报背景图片URL',
                \`poster_qr_code_size\` int(11) NOT NULL DEFAULT 202 COMMENT '二维码大小（像素）',
                \`poster_qr_code_position_x\` int(11) NOT NULL DEFAULT 200 COMMENT '二维码X坐标位置',
                \`poster_qr_code_position_y\` int(11) NOT NULL DEFAULT 400 COMMENT '二维码Y坐标位置',
                -- 分享配置
                \`share_title\` varchar(200) NOT NULL DEFAULT 'AI智能体使用邀请' COMMENT '分享标题',
                \`share_description\` varchar(500) NOT NULL DEFAULT '注册即可获得额外免费使用次数，快来体验智能AI助手！' COMMENT '分享描述',
                -- 奖励配置
                \`invite_reward_points\` int(11) NOT NULL DEFAULT 100 COMMENT '邀请奖励点数',
                \`register_reward_points\` int(11) NOT NULL DEFAULT 50 COMMENT '注册奖励点数',
                -- 邀请配置
                \`inviter_reward\` int(11) NOT NULL DEFAULT 50 COMMENT '邀请人奖励',
                \`max_invite_count\` int(11) NOT NULL DEFAULT 10 COMMENT '最大邀请次数',
                \`reward_valid_days\` int(11) NOT NULL DEFAULT 60 COMMENT '奖励有效期（天）',
                \`invite_success_message\` varchar(500) NOT NULL DEFAULT '恭喜您成功邀请好友，获得{reward}次免费使用奖励！' COMMENT '邀请成功提示信息',
                -- 合伙人配置
                \`invitee_reward\` int(11) NOT NULL DEFAULT 30 COMMENT '被邀请人奖励',
                \`promoter_cashback\` decimal(5,2) NOT NULL DEFAULT 5.00 COMMENT '推广人返现比例（%）',
                \`promoter_commission\` decimal(5,2) NOT NULL DEFAULT 2.00 COMMENT '推广人佣金比例（%）',
                \`min_withdrawal\` decimal(10,2) NOT NULL DEFAULT 1.00 COMMENT '最低提现金额',
                \`withdrawal_channel\` varchar(20) NOT NULL DEFAULT 'alipay' COMMENT '提现渠道',
                \`withdrawal_fee\` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT '提现手续费（%）',
                \`created_at\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                \`updated_at\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                PRIMARY KEY (\`id\`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='推荐奖励配置表'
        `);

        // 9. 创建系统配置表
        await queryRunner.query(`
            CREATE TABLE IF NOT EXISTS \`config_system\` (
                \`id\` int(11) NOT NULL AUTO_INCREMENT,
                \`site_name\` varchar(100) NOT NULL DEFAULT 'AI Agent Admin' COMMENT '站点名称',
                \`site_logo\` varchar(500) NOT NULL DEFAULT '' COMMENT '站点Logo图片URL',
                \`site_description\` varchar(500) NOT NULL DEFAULT '专业的AI智能体管理平台' COMMENT '站点描述',
                \`copyright\` varchar(200) NOT NULL DEFAULT '© 2023 AI Agent Admin' COMMENT '版权信息',
                \`contact_email\` varchar(100) NOT NULL DEFAULT '<EMAIL>' COMMENT '联系邮箱',
                \`beian_info\` varchar(100) NOT NULL DEFAULT '' COMMENT '备案信息',
                \`site_url\` varchar(255) NOT NULL DEFAULT '' COMMENT '站点URL',
                \`max_login_attempts\` int(11) NOT NULL DEFAULT 5 COMMENT '最大登录尝试次数',
                \`login_lock_time\` int(11) NOT NULL DEFAULT 30 COMMENT '登录锁定时间(分钟)',
                \`session_timeout\` int(11) NOT NULL DEFAULT 120 COMMENT '会话过期时间(分钟)',
                \`maintenance_mode\` tinyint(1) NOT NULL DEFAULT 0 COMMENT '维护模式(true/false)',
                \`primary_color\` varchar(20) NOT NULL DEFAULT '#1890ff' COMMENT '主题色',
                \`created_at\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                \`updated_at\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                PRIMARY KEY (\`id\`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表'
        `);

        // 插入初始化数据
        await this.insertInitialData(queryRunner);
    }

    private async insertInitialData(queryRunner: QueryRunner): Promise<void> {
        // 初始化推荐奖励配置
        await queryRunner.query(`
            INSERT INTO \`config_referral\` (\`enabled\`, \`poster_qr_code_size\`, \`poster_qr_code_position_x\`, \`poster_qr_code_position_y\`, \`share_title\`, \`share_description\`, \`invite_reward_points\`, \`register_reward_points\`, \`inviter_reward\`, \`max_invite_count\`, \`reward_valid_days\`, \`invite_success_message\`, \`invitee_reward\`, \`promoter_cashback\`, \`promoter_commission\`, \`min_withdrawal\`, \`withdrawal_channel\`, \`withdrawal_fee\`) 
            VALUES (0, 202, 200, 400, 'AI智能体使用邀请', '注册即可获得额外免费使用次数，快来体验智能AI助手！', 100, 50, 50, 10, 60, '恭喜您成功邀请好友，获得{reward}次免费使用奖励！', 30, 5.00, 2.00, 1.00, 'alipay', 0.00) 
            ON DUPLICATE KEY UPDATE \`id\` = \`id\`
        `);

        // 初始化系统配置
        await queryRunner.query(`
            INSERT INTO \`config_system\` (\`site_name\`, \`site_description\`, \`copyright\`, \`contact_email\`, \`max_login_attempts\`, \`login_lock_time\`, \`session_timeout\`, \`maintenance_mode\`, \`primary_color\`) 
            VALUES ('AI Agent Admin', '专业的AI智能体管理平台', '© 2023 AI Agent Admin', '<EMAIL>', 5, 30, 120, 0, '#1890ff') 
            ON DUPLICATE KEY UPDATE \`id\` = \`id\`
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE IF EXISTS \`config_system\``);
        await queryRunner.query(`DROP TABLE IF EXISTS \`config_referral\``);
    }
}
