/**
 * 微信支付配置实体类
 */
import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('config_wechat_pay')
export class ConfigWechatPay {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'tinyint', width: 1, default: 0, comment: '是否启用' })
  enabled: boolean;

  @Column({ type: 'tinyint', width: 1, default: 1, comment: '是否为沙箱环境' })
  sandbox: boolean;

  @Column({ name: 'mch_id', length: 50, default: '', comment: '微信支付商户号' })
  mchId: string;

  @Column({ name: 'api_v3_key', length: 255, default: '', comment: 'API v3密钥' })
  apiV3Key: string;

  @Column({ name: 'api_key', length: 255, default: '', comment: 'API v2密钥' })
  apiKey: string;

  @Column({ name: 'serial_no', length: 100, default: '', comment: '证书序列号' })
  serialNo: string;

  @Column({ name: 'certificate_content', type: 'text', nullable: true, comment: '证书内容' })
  certificateContent: string;

  @Column({ name: 'private_key_content', type: 'text', nullable: true, comment: '私钥内容' })
  privateKeyContent: string;

  @Column({ name: 'notify_url', length: 255, default: '', comment: '支付结果通知URL' })
  notifyUrl: string;

  @CreateDateColumn({ name: 'created_at', comment: '创建时间' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', comment: '更新时间' })
  updatedAt: Date;
}
