import { AppDataSource } from '../data-source';

async function checkUserPackageStatus() {
  try {
    await AppDataSource.initialize();
    console.log('✅ 数据库连接已建立\n');

    // 查看所有用户的套餐状态
    console.log('=== 用户套餐状态 ===');
    const users = await AppDataSource.query(`
      SELECT m.id, m.name, m.packageId, m.packageExpiredAt, 
             p.title, p.dailyMaxConsumption, p.price
      FROM members m
      LEFT JOIN package p ON m.packageId = p.id
      WHERE m.packageId IS NOT NULL
      ORDER BY m.id ASC
    `);

    console.log('📋 当前有套餐的用户:');
    users.forEach((user: any) => {
      const isExpired = user.packageExpiredAt && new Date(user.packageExpiredAt) <= new Date();
      const isUnlimited = user.dailyMaxConsumption === 0 || user.dailyMaxConsumption === -1;
      const status = isExpired ? '已过期' : '有效';
      const type = isUnlimited ? '无限制' : `限制(${user.dailyMaxConsumption}/天)`;
      
      console.log(`  用户${user.id} (${user.name}): ${user.title} - ${type} - ${status}`);
      if (user.packageExpiredAt) {
        console.log(`    到期时间: ${new Date(user.packageExpiredAt).toLocaleString()}`);
      }
    });

    // 查看所有可用套餐
    console.log('\n=== 可用套餐列表 ===');
    const packages = await AppDataSource.query(`
      SELECT id, title, price, duration, totalQuota, dailyMaxConsumption, enabled
      FROM package 
      WHERE enabled = 1
      ORDER BY id ASC
    `);

    console.log('📦 可用套餐:');
    packages.forEach((pkg: any) => {
      const isUnlimited = pkg.dailyMaxConsumption === 0 || pkg.dailyMaxConsumption === -1;
      const type = isUnlimited ? '无限制' : `限制(${pkg.dailyMaxConsumption}/天)`;
      console.log(`  ${pkg.id}. ${pkg.title} - ¥${pkg.price} - ${type} - ${pkg.duration}天`);
    });

    // 测试套餐购买限制逻辑
    console.log('\n=== 测试套餐购买限制 ===');
    
    // 找一个有无限制套餐的用户
    const unlimitedUser = users.find((u: any) => 
      u.packageExpiredAt && new Date(u.packageExpiredAt) > new Date() && 
      (u.dailyMaxConsumption === 0 || u.dailyMaxConsumption === -1)
    );

    if (unlimitedUser) {
      console.log(`\n测试用户: ${unlimitedUser.name} (ID: ${unlimitedUser.id})`);
      console.log(`当前套餐: ${unlimitedUser.title} (无限制)`);
      console.log(`到期时间: ${new Date(unlimitedUser.packageExpiredAt).toLocaleString()}`);
      
      // 找一个限制套餐来测试
      const limitedPackage = packages.find((p: any) => p.dailyMaxConsumption > 0);
      if (limitedPackage) {
        console.log(`\n尝试购买限制套餐: ${limitedPackage.title}`);
        
        // 模拟购买限制检查
        const currentPackage = await AppDataSource.query(`
          SELECT * FROM package WHERE id = ?
        `, [unlimitedUser.packageId]);
        
        if (currentPackage.length > 0) {
          const pkg = currentPackage[0];
          const isCurrentUnlimited = pkg.dailyMaxConsumption === 0 || pkg.dailyMaxConsumption === -1;
          const isTargetUnlimited = limitedPackage.dailyMaxConsumption === 0 || limitedPackage.dailyMaxConsumption === -1;
          
          console.log(`当前套餐是否无限制: ${isCurrentUnlimited}`);
          console.log(`目标套餐是否无限制: ${isTargetUnlimited}`);
          
          if (!isTargetUnlimited) {
            // 检查是否已有有效的限制套餐
            if (unlimitedUser.packageExpiredAt && new Date(unlimitedUser.packageExpiredAt) > new Date()) {
              if (pkg.dailyMaxConsumption > 0) {
                console.log('❌ 购买限制: 已有有效的限制套餐');
              } else {
                console.log('✅ 可以购买: 当前是无限制套餐，可以购买限制套餐');
              }
            } else {
              console.log('✅ 可以购买: 当前套餐已过期');
            }
          } else {
            console.log('✅ 可以购买: 目标是无限制套餐');
          }
        }
      }
    }

  } catch (error) {
    console.error('❌ 检查失败:', error);
  } finally {
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('\n🔌 数据库连接已关闭');
    }
  }
}

// 运行检查
checkUserPackageStatus().catch(console.error);
