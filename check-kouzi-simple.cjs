const mysql = require('mysql2/promise');

async function checkKouziConfig() {
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'root',
      database: 'ai_agent'
    });

    console.log('查询扣子配置数据...');
    const [rows] = await connection.execute('SELECT * FROM config_kouzi ORDER BY id DESC LIMIT 10');
    
    if (rows.length > 0) {
      console.log('扣子配置数据:');
      rows.forEach((row, index) => {
        console.log(`记录 ${index + 1}:`, JSON.stringify(row, null, 2));
      });
    } else {
      console.log('扣子配置表为空');
    }
    
    await connection.end();
  } catch (error) {
    console.error('查询失败:', error.message);
  }
}

checkKouziConfig();
