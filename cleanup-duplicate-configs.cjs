const mysql = require('mysql2/promise');

async function cleanupDuplicateConfigs() {
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'root',
      database: 'ai_agent'
    });

    console.log('连接到数据库成功');

    // 清理config_system表的重复记录，只保留第一条
    console.log('清理config_system表的重复记录...');
    await connection.execute(`
      DELETE FROM config_system 
      WHERE id NOT IN (
        SELECT * FROM (
          SELECT MIN(id) FROM config_system
        ) AS temp
      )
    `);

    // 清理config_kouzi表的重复记录，只保留第一条
    console.log('清理config_kouzi表的重复记录...');
    await connection.execute(`
      DELETE FROM config_kouzi 
      WHERE id NOT IN (
        SELECT * FROM (
          SELECT MIN(id) FROM config_kouzi
        ) AS temp
      )
    `);

    // 检查其他配置表是否有重复记录
    const configTables = [
      'config_wechat_official',
      'config_wechat_miniprogram', 
      'config_wechat_pay',
      'config_alipay',
      'config_verification',
      'config_free_quota',
      'config_referral'
    ];

    for (const table of configTables) {
      console.log(`检查${table}表...`);
      const [rows] = await connection.execute(`SELECT COUNT(*) as count FROM ${table}`);
      if (rows[0].count > 1) {
        console.log(`清理${table}表的重复记录...`);
        await connection.execute(`
          DELETE FROM ${table} 
          WHERE id NOT IN (
            SELECT * FROM (
              SELECT MIN(id) FROM ${table}
            ) AS temp
          )
        `);
      }
    }

    console.log('清理完成，查看最终结果:');
    
    // 查看清理后的config_system表
    const [systemData] = await connection.execute("SELECT * FROM config_system");
    console.log('config_system表数据:');
    console.log(systemData);

    // 查看清理后的config_kouzi表
    const [kouziData] = await connection.execute("SELECT * FROM config_kouzi");
    console.log('config_kouzi表数据:');
    console.log(kouziData);

    await connection.end();
    console.log('数据库清理完成');
  } catch (error) {
    console.error('清理失败:', error);
  }
}

cleanupDuplicateConfigs();
