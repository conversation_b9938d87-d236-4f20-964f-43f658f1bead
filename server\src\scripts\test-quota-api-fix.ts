import { AppDataSource } from '../data-source';
import { DailyLimitService } from '../services/daily-limit.service';

async function testQuotaApiFix() {
  try {
    await AppDataSource.initialize();
    console.log('✅ 数据库连接已建立\n');

    const testUserId = 65; // 使用您的用户ID

    console.log('=== 测试修复后的额度详情API ===');
    
    // 调用修复后的 getQuotaDetailsByCategory 方法
    const quotaDetails = await DailyLimitService.getQuotaDetailsByCategory(testUserId);
    
    console.log('📊 额度详情API响应:');
    console.log(JSON.stringify(quotaDetails, null, 2));
    
    // 验证结果
    console.log('\n=== 验证结果 ===');
    
    // 1. 检查免费额度
    console.log('1. 免费额度:');
    console.log(`   标题: ${quotaDetails.freeQuota.title}`);
    console.log(`   日限额: ${quotaDetails.freeQuota.dailyLimit}`);
    console.log(`   今日已用: ${quotaDetails.freeQuota.todayUsed}`);
    console.log(`   今日剩余: ${quotaDetails.freeQuota.remaining}`);
    
    // 2. 检查限制套餐
    console.log('\n2. 限制套餐:');
    if (quotaDetails.limitedPackages.length > 0) {
      quotaDetails.limitedPackages.forEach((pkg: any, index: number) => {
        console.log(`   套餐${index + 1}: ${pkg.title}`);
        console.log(`   总点数: ${pkg.totalPoints}`);
        console.log(`   日限额: ${pkg.dailyLimit}`);
        console.log(`   今日已用: ${pkg.todayUsed}`);
        console.log(`   今日剩余: ${pkg.remaining}`);
        console.log(`   是否有效: ${pkg.isActive ? '是' : '否'}`);
        if (pkg.expiredAt) {
          console.log(`   到期时间: ${new Date(pkg.expiredAt).toLocaleString()}`);
        }
        if (pkg.note) {
          console.log(`   备注: ${pkg.note}`);
        }
      });
      console.log('   ✅ 限制套餐信息已正确显示');
    } else {
      console.log('   ❌ 没有显示限制套餐信息');
    }
    
    // 3. 检查无限制套餐
    console.log('\n3. 无限制套餐:');
    if (quotaDetails.unlimitedPackages.length > 0) {
      quotaDetails.unlimitedPackages.forEach((pkg: any, index: number) => {
        console.log(`   套餐${index + 1}: ${pkg.title}`);
        console.log(`   总点数: ${pkg.totalPoints}`);
        console.log(`   日限额: ${pkg.dailyLimit === -1 ? '无限制' : pkg.dailyLimit}`);
        console.log(`   今日已用: ${pkg.todayUsed}`);
        console.log(`   今日剩余: ${pkg.remaining === -1 ? '无限制' : pkg.remaining}`);
        if (pkg.expiredAt) {
          console.log(`   到期时间: ${new Date(pkg.expiredAt).toLocaleString()}`);
        }
      });
      console.log('   ✅ 无限制套餐信息已正确显示');
    } else {
      console.log('   ❌ 没有显示无限制套餐信息');
    }
    
    // 4. 总结
    console.log('\n=== 修复总结 ===');
    const hasLimitedPackages = quotaDetails.limitedPackages.length > 0;
    const hasUnlimitedPackages = quotaDetails.unlimitedPackages.length > 0;
    
    if (hasLimitedPackages && hasUnlimitedPackages) {
      console.log('✅ 修复成功: 现在同时显示限制套餐和无限制套餐');
    } else if (hasLimitedPackages) {
      console.log('✅ 显示限制套餐信息');
    } else if (hasUnlimitedPackages) {
      console.log('✅ 显示无限制套餐信息');
    } else {
      console.log('❌ 没有显示任何套餐信息');
    }
    
    console.log(`总今日使用: ${quotaDetails.totalTodayUsed}`);

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('\n🔌 数据库连接已关闭');
    }
  }
}

// 运行测试
testQuotaApiFix().catch(console.error);
