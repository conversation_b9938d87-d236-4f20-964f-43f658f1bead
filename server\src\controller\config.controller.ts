/**
 * 配置管理控制器
 */
import { Request, Response } from 'express';
import { In } from 'typeorm';
import { AppDataSource } from '../data-source';
import { Config } from '../entity/Config';
import { ConfigChangeLog } from '../entity/ConfigChangeLog';
import { ConfigWechatOfficial } from '../entity/ConfigWechatOfficial';
import { ConfigWechatMiniprogram } from '../entity/ConfigWechatMiniprogram';
import { ConfigWechatPay } from '../entity/ConfigWechatPay';
import { ConfigAlipay } from '../entity/ConfigAlipay';
import { ConfigKouzi } from '../entity/ConfigKouzi';
import { ConfigVerification } from '../entity/ConfigVerification';
import { ConfigFreeQuota } from '../entity/ConfigFreeQuota';
import { ConfigReferral } from '../entity/ConfigReferral';
import { ConfigSystem } from '../entity/ConfigSystem';

export class ConfigController {
  
  /**
   * 从新的独立配置表获取配置数据
   * @param configType 配置类型
   * @returns 配置数据或null
   */
  private async getNewConfigByType(configType: string): Promise<any | null> {
    try {
      console.log(`[DEBUG 2025-08-02 NEW VERSION] getNewConfigByType called with configType: ${configType}`);
      switch (configType) {
        case 'wechat-official':
          const wechatOfficialRepo = AppDataSource.getRepository(ConfigWechatOfficial);
          const wechatOfficialConfig = await wechatOfficialRepo.findOne({ where: { id: 1 } });
          return wechatOfficialConfig ? {
            enabled: Boolean(wechatOfficialConfig.enabled),
            appId: wechatOfficialConfig.appId,
            appSecret: wechatOfficialConfig.appSecret,
            token: wechatOfficialConfig.token,
            encodingAesKey: wechatOfficialConfig.encodingAesKey,
            verifyFileName: wechatOfficialConfig.verifyFileName,
            verifyFileContent: wechatOfficialConfig.verifyFileContent
          } : null;

        case 'wechat-miniprogram':
          const wechatMiniprogramRepo = AppDataSource.getRepository(ConfigWechatMiniprogram);
          const wechatMiniprogramConfig = await wechatMiniprogramRepo.findOne({ where: { id: 1 } });
          return wechatMiniprogramConfig ? {
            enabled: Boolean(wechatMiniprogramConfig.enabled),
            appId: wechatMiniprogramConfig.appId,
            appSecret: wechatMiniprogramConfig.appSecret,
            verifyFileName: wechatMiniprogramConfig.verifyFileName,
            verifyFileContent: wechatMiniprogramConfig.verifyFileContent,
            bannerAdEnabled: Boolean(wechatMiniprogramConfig.bannerAdEnabled),
            bannerAdId: wechatMiniprogramConfig.bannerAdId,
            interstitialAdEnabled: Boolean(wechatMiniprogramConfig.interstitialAdEnabled),
            interstitialAdId: wechatMiniprogramConfig.interstitialAdId,
            interstitialAdCount: wechatMiniprogramConfig.interstitialAdCount,
            interstitialAdInterval: wechatMiniprogramConfig.interstitialAdInterval,
            videoAdEnabled: Boolean(wechatMiniprogramConfig.videoAdEnabled),
            videoAdId: wechatMiniprogramConfig.videoAdId
          } : null;

        case 'wechat-pay':
          const wechatPayRepo = AppDataSource.getRepository(ConfigWechatPay);
          const wechatPayConfig = await wechatPayRepo.findOne({ where: { id: 1 } });
          return wechatPayConfig ? {
            enabled: wechatPayConfig.enabled,
            sandbox: wechatPayConfig.sandbox,
            mchId: wechatPayConfig.mchId,
            apiV3Key: wechatPayConfig.apiV3Key,
            apiKey: wechatPayConfig.apiKey,
            serialNo: wechatPayConfig.serialNo,
            notifyUrl: wechatPayConfig.notifyUrl,
            // 返回证书和私钥的对象格式，以便前端正确处理
            certificate: wechatPayConfig.certificateContent ? { content: wechatPayConfig.certificateContent } : null,
            privateKey: wechatPayConfig.privateKeyContent ? { content: wechatPayConfig.privateKeyContent } : null
          } : null;

        case 'alipay':
          const alipayRepo = AppDataSource.getRepository(ConfigAlipay);
          const alipayConfig = await alipayRepo.findOne({ where: { id: 1 } });
          return alipayConfig ? {
            enabled: alipayConfig.enabled,
            sandboxMode: alipayConfig.sandboxMode,
            appId: alipayConfig.appId,
            privateKey: alipayConfig.privateKey,
            alipayPublicKey: alipayConfig.alipayPublicKey,
            signType: alipayConfig.signType,
            notifyUrl: alipayConfig.notifyUrl
          } : null;

        case 'kouzi':
          const kouziRepo = AppDataSource.getRepository(ConfigKouzi);
          const kouziConfig = await kouziRepo.findOne({
            where: {},
            order: { id: 'ASC' }
          });
          console.log('扣子配置查询结果:', kouziConfig);
          return kouziConfig ? {
            enabled: Boolean(kouziConfig.enabled),
            appId: kouziConfig.appId,
            privateKey: kouziConfig.privateKey,
            publicKey: kouziConfig.publicKey,
            personalAccessToken: kouziConfig.personalAccessToken,
            authMode: kouziConfig.authMode
          } : null;

        case 'verification':
          const verificationRepo = AppDataSource.getRepository(ConfigVerification);
          const verificationConfig = await verificationRepo.findOne({ where: { id: 1 } });
          return verificationConfig ? {
            smsProvider: verificationConfig.smsProvider,
            emailProvider: verificationConfig.emailProvider,
            storageProvider: verificationConfig.storageProvider,
            // SMS配置
            smsAliyunAccessKeyId: verificationConfig.smsAliyunAccessKeyId,
            smsAliyunAccessKeySecret: verificationConfig.smsAliyunAccessKeySecret,
            smsAliyunSignName: verificationConfig.smsAliyunSignName,
            smsAliyunTemplateCode: verificationConfig.smsAliyunTemplateCode,
            // 邮件配置
            emailSmtpHost: verificationConfig.emailSmtpHost,
            emailSmtpPort: verificationConfig.emailSmtpPort,
            emailSmtpFromEmail: verificationConfig.emailSmtpFromEmail,
            emailSmtpPassword: verificationConfig.emailSmtpPassword,
            emailSmtpFromName: verificationConfig.emailSmtpFromName,
            emailSmtpSsl: verificationConfig.emailSmtpSsl,
            // 存储配置
            storageAliyunAccessKeyId: verificationConfig.storageAliyunAccessKeyId,
            storageAliyunAccessKeySecret: verificationConfig.storageAliyunAccessKeySecret,
            storageAliyunBucket: verificationConfig.storageAliyunBucket,
            storageAliyunEndpoint: verificationConfig.storageAliyunEndpoint,
            storageAliyunBaseUrl: verificationConfig.storageAliyunBaseUrl
          } : null;

        case 'free-quota':
          const freeQuotaRepo = AppDataSource.getRepository(ConfigFreeQuota);
          const freeQuotaConfig = await freeQuotaRepo.findOne({ where: { id: 1 } });
          return freeQuotaConfig ? {
            enabled: freeQuotaConfig.enabled,
            newUserQuota: freeQuotaConfig.newUserQuota,
            quotaValidDays: freeQuotaConfig.quotaValidDays,
            maxConsumePerSession: freeQuotaConfig.maxConsumePerSession,
            insufficientQuotaMessage: freeQuotaConfig.insufficientQuotaMessage
          } : null;

        case 'referral':
          const referralRepo = AppDataSource.getRepository(ConfigReferral);
          const referralConfig = await referralRepo.findOne({ where: { id: 1 } });
          return referralConfig ? {
            enabled: referralConfig.enabled,
            posterBackgroundImage: referralConfig.posterBackgroundImage,
            posterQrCodeSize: referralConfig.posterQrCodeSize,
            shareTitle: referralConfig.shareTitle,
            shareDescription: referralConfig.shareDescription,
            inviteRewardPoints: referralConfig.inviteRewardPoints,
            registerRewardPoints: referralConfig.registerRewardPoints,
            inviterReward: referralConfig.inviterReward,
            maxInviteCount: referralConfig.maxInviteCount,
            rewardValidDays: referralConfig.rewardValidDays
          } : null;

        case 'system':
        case 'system-basic':
        case 'system-logo':
        case 'system-login':
        case 'system-advanced':
          console.log(`[DEBUG] Processing system config type: ${configType}`);
          const systemRepo = AppDataSource.getRepository(ConfigSystem);
          const systemConfig = await systemRepo.findOne({
            where: {},
            order: { id: 'ASC' }
          });
          if (!systemConfig) return null;

          // 根据不同的子类型返回相应的配置数据
          switch (configType) {
            case 'system-basic':
              return {
                siteName: systemConfig.siteName,
                siteDescription: systemConfig.siteDescription,
                adminEmail: systemConfig.contactEmail,
                copyright: systemConfig.copyright,
                beianInfo: systemConfig.beianInfo,
                siteUrl: systemConfig.siteUrl
              };
            case 'system-logo':
              return {
                siteLogo: systemConfig.siteLogo,
                mainLogo: systemConfig.mainLogo,
                sidebarLogo: systemConfig.sidebarLogo,
                miniLogo: systemConfig.miniLogo,
                userLogo: systemConfig.userLogo,
                favicon: systemConfig.favicon,
                customerServiceImage: systemConfig.customerServiceImage,
                primaryColor: systemConfig.primaryColor
              };
            case 'system-login':
              return {
                maxLoginAttempts: systemConfig.maxLoginAttempts,
                loginLockTime: systemConfig.loginLockTime,
                sessionTimeout: systemConfig.sessionTimeout
              };
            case 'system-advanced':
              return {
                maintenanceMode: systemConfig.maintenanceMode
              };
            default: // 'system'
              return {
                siteName: systemConfig.siteName,
                siteLogo: systemConfig.siteLogo,
                mainLogo: systemConfig.mainLogo,
                sidebarLogo: systemConfig.sidebarLogo,
                miniLogo: systemConfig.miniLogo,
                userLogo: systemConfig.userLogo,
                favicon: systemConfig.favicon,
                customerServiceImage: systemConfig.customerServiceImage,
                siteDescription: systemConfig.siteDescription,
                copyright: systemConfig.copyright,
                contactEmail: systemConfig.contactEmail,
                beianInfo: systemConfig.beianInfo,
                siteUrl: systemConfig.siteUrl,
                maxLoginAttempts: systemConfig.maxLoginAttempts,
                loginLockTime: systemConfig.loginLockTime,
                sessionTimeout: systemConfig.sessionTimeout,
                maintenanceMode: systemConfig.maintenanceMode,
                primaryColor: systemConfig.primaryColor
              };
          }

        default:
          return null;
      }
    } catch (error) {
      console.error(`获取新配置表数据失败 (${configType}):`, error);
      return null;
    }
  }

  /**
   * 前端兼容 - 获取指定类型的配置（前端格式）
   * @param req - 请求对象
   * @param res - 响应对象
   */
  async getFrontendConfigByType(req: Request, res: Response) {
    const { configType } = req.params;

    if (!configType) {
      return res.status(400).json({
        code: 400,
        message: '配置类型不能为空'
      });
    }

    try {
      // 检查配置类型是否支持
      const supportedTypes = [
        'wechat-official', 'wechat-miniprogram', 'wechat-pay', 'alipay',
        'kouzi', 'verification', 'free-quota', 'referral',
        'system', 'system-basic', 'system-logo', 'system-login', 'system-advanced'
      ];

      if (!supportedTypes.includes(configType)) {
        return res.status(400).json({
          code: 400,
          message: `不支持的配置类型: ${configType}`,
          data: null
        });
      }

      // 直接使用新的独立配置表
      const newConfigResult = await this.getNewConfigByType(configType);

      // 即使没有数据也返回成功，只是 configValue 为 null
      return res.status(200).json({
        code: 200,
        message: '获取配置成功',
        data: {
          configType,
          configValue: newConfigResult,
          lastUpdated: new Date()
        }
      });
    } catch (error) {
      console.error('获取配置失败:', error);
      return res.status(500).json({
        code: 500,
        message: '获取配置失败',
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 保存配置到新的独立配置表
   * @param configType 配置类型
   * @param configValue 配置值
   * @returns 是否成功保存
   */
  private async saveToNewConfigTable(configType: string, configValue: any): Promise<boolean> {
    try {
      switch (configType) {
        case 'wechat-official':
          const wechatOfficialRepo = AppDataSource.getRepository(ConfigWechatOfficial);
          let wechatOfficialConfig = await wechatOfficialRepo.findOne({ where: { id: 1 } });

          if (wechatOfficialConfig) {
            wechatOfficialConfig.enabled = configValue.enabled || false;
            wechatOfficialConfig.appId = configValue.appId || '';
            wechatOfficialConfig.appSecret = configValue.appSecret || '';
            if (configValue.token !== undefined) wechatOfficialConfig.token = configValue.token;
            if (configValue.encodingAesKey !== undefined) wechatOfficialConfig.encodingAesKey = configValue.encodingAesKey;
            if (configValue.verifyFileName !== undefined) wechatOfficialConfig.verifyFileName = configValue.verifyFileName;
            if (configValue.verifyFileContent !== undefined) wechatOfficialConfig.verifyFileContent = configValue.verifyFileContent;
          } else {
            wechatOfficialConfig = wechatOfficialRepo.create({
              enabled: configValue.enabled || false,
              appId: configValue.appId || '',
              appSecret: configValue.appSecret || '',
              token: configValue.token || '',
              encodingAesKey: configValue.encodingAesKey || '',
              verifyFileName: configValue.verifyFileName || '',
              verifyFileContent: configValue.verifyFileContent || ''
            });
          }

          await wechatOfficialRepo.save(wechatOfficialConfig);
          return true;

        case 'wechat-miniprogram':
          const wechatMiniprogramRepo = AppDataSource.getRepository(ConfigWechatMiniprogram);
          let wechatMiniprogramConfig = await wechatMiniprogramRepo.findOne({ where: { id: 1 } });

          if (wechatMiniprogramConfig) {
            wechatMiniprogramConfig.enabled = configValue.enabled || false;
            wechatMiniprogramConfig.appId = configValue.appId || '';
            wechatMiniprogramConfig.appSecret = configValue.appSecret || '';
            if (configValue.verifyFileName !== undefined) wechatMiniprogramConfig.verifyFileName = configValue.verifyFileName;
            if (configValue.verifyFileContent !== undefined) wechatMiniprogramConfig.verifyFileContent = configValue.verifyFileContent;
            if (configValue.bannerAdEnabled !== undefined) wechatMiniprogramConfig.bannerAdEnabled = configValue.bannerAdEnabled;
            if (configValue.bannerAdId !== undefined) wechatMiniprogramConfig.bannerAdId = configValue.bannerAdId;
            if (configValue.interstitialAdEnabled !== undefined) wechatMiniprogramConfig.interstitialAdEnabled = configValue.interstitialAdEnabled;
            if (configValue.interstitialAdId !== undefined) wechatMiniprogramConfig.interstitialAdId = configValue.interstitialAdId;
            if (configValue.interstitialAdCount !== undefined) wechatMiniprogramConfig.interstitialAdCount = configValue.interstitialAdCount;
            if (configValue.interstitialAdInterval !== undefined) wechatMiniprogramConfig.interstitialAdInterval = configValue.interstitialAdInterval;
            if (configValue.videoAdEnabled !== undefined) wechatMiniprogramConfig.videoAdEnabled = configValue.videoAdEnabled;
            if (configValue.videoAdId !== undefined) wechatMiniprogramConfig.videoAdId = configValue.videoAdId;
          } else {
            wechatMiniprogramConfig = wechatMiniprogramRepo.create({
              enabled: configValue.enabled || false,
              appId: configValue.appId || '',
              appSecret: configValue.appSecret || '',
              verifyFileName: configValue.verifyFileName || '',
              verifyFileContent: configValue.verifyFileContent || '',
              bannerAdEnabled: configValue.bannerAdEnabled || false,
              bannerAdId: configValue.bannerAdId || '',
              interstitialAdEnabled: configValue.interstitialAdEnabled || false,
              interstitialAdId: configValue.interstitialAdId || '',
              interstitialAdCount: configValue.interstitialAdCount || 0,
              interstitialAdInterval: configValue.interstitialAdInterval || 0,
              videoAdEnabled: configValue.videoAdEnabled || false,
              videoAdId: configValue.videoAdId || ''
            });
          }

          await wechatMiniprogramRepo.save(wechatMiniprogramConfig);
          return true;

        case 'wechat-pay':
          const wechatPayRepo = AppDataSource.getRepository(ConfigWechatPay);
          let wechatPayConfig = await wechatPayRepo.findOne({ where: { id: 1 } });

          if (wechatPayConfig) {
            wechatPayConfig.enabled = configValue.enabled || false;
            wechatPayConfig.sandbox = configValue.sandbox || false;
            wechatPayConfig.mchId = configValue.mchId || '';
            wechatPayConfig.apiV3Key = configValue.apiV3Key || '';
            wechatPayConfig.apiKey = configValue.apiKey || '';
            wechatPayConfig.serialNo = configValue.serialNo || '';

            // 处理证书内容 - 支持对象格式和字符串格式
            if (configValue.certificate && typeof configValue.certificate === 'object' && configValue.certificate.content) {
              wechatPayConfig.certificateContent = configValue.certificate.content;
            } else if (configValue.certificateContent) {
              wechatPayConfig.certificateContent = configValue.certificateContent;
            }

            // 处理私钥内容 - 支持对象格式和字符串格式
            if (configValue.privateKey && typeof configValue.privateKey === 'object' && configValue.privateKey.content) {
              wechatPayConfig.privateKeyContent = configValue.privateKey.content;
            } else if (configValue.privateKeyContent) {
              wechatPayConfig.privateKeyContent = configValue.privateKeyContent;
            }

            wechatPayConfig.notifyUrl = configValue.notifyUrl || '';
          } else {
            // 处理证书和私钥内容
            let certificateContent = '';
            let privateKeyContent = '';

            if (configValue.certificate && typeof configValue.certificate === 'object' && configValue.certificate.content) {
              certificateContent = configValue.certificate.content;
            } else if (configValue.certificateContent) {
              certificateContent = configValue.certificateContent;
            }

            if (configValue.privateKey && typeof configValue.privateKey === 'object' && configValue.privateKey.content) {
              privateKeyContent = configValue.privateKey.content;
            } else if (configValue.privateKeyContent) {
              privateKeyContent = configValue.privateKeyContent;
            }

            wechatPayConfig = wechatPayRepo.create({
              enabled: configValue.enabled || false,
              sandbox: configValue.sandbox || false,
              mchId: configValue.mchId || '',
              apiV3Key: configValue.apiV3Key || '',
              apiKey: configValue.apiKey || '',
              serialNo: configValue.serialNo || '',
              certificateContent: certificateContent,
              privateKeyContent: privateKeyContent,
              notifyUrl: configValue.notifyUrl || ''
            });
          }

          await wechatPayRepo.save(wechatPayConfig);
          return true;

        case 'alipay':
          const alipayRepo = AppDataSource.getRepository(ConfigAlipay);
          let alipayConfig = await alipayRepo.findOne({ where: { id: 1 } });

          if (alipayConfig) {
            alipayConfig.enabled = configValue.enabled || false;
            alipayConfig.sandboxMode = configValue.sandboxMode || false;
            alipayConfig.appId = configValue.appId || '';
            alipayConfig.privateKey = configValue.privateKey || '';
            alipayConfig.alipayPublicKey = configValue.alipayPublicKey || '';
            alipayConfig.signType = configValue.signType || 'RSA2';
            alipayConfig.notifyUrl = configValue.notifyUrl || '';
          } else {
            alipayConfig = alipayRepo.create({
              enabled: configValue.enabled || false,
              sandboxMode: configValue.sandboxMode || false,
              appId: configValue.appId || '',
              privateKey: configValue.privateKey || '',
              alipayPublicKey: configValue.alipayPublicKey || '',
              signType: configValue.signType || 'RSA2',
              notifyUrl: configValue.notifyUrl || ''
            });
          }

          await alipayRepo.save(alipayConfig);
          return true;

        case 'kouzi':
          const kouziRepo = AppDataSource.getRepository(ConfigKouzi);
          let kouziConfig = await kouziRepo.findOne({
            where: {},
            order: { id: 'ASC' }
          });
          console.log('保存时查询到的扣子配置:', kouziConfig);

          if (kouziConfig) {
            kouziConfig.enabled = configValue.enabled || false;
            kouziConfig.appId = configValue.appId || '';
            kouziConfig.privateKey = configValue.privateKey || '';
            kouziConfig.publicKey = configValue.publicKey || '';
            kouziConfig.personalAccessToken = configValue.personalAccessToken || '';
            kouziConfig.authMode = configValue.authMode || 'jwt';
          } else {
            kouziConfig = kouziRepo.create({
              enabled: configValue.enabled || false,
              appId: configValue.appId || '',
              privateKey: configValue.privateKey || '',
              publicKey: configValue.publicKey || '',
              personalAccessToken: configValue.personalAccessToken || '',
              authMode: configValue.authMode || 'jwt'
            });
          }

          await kouziRepo.save(kouziConfig);
          return true;

        case 'system':
        case 'system-basic':
        case 'system-logo':
        case 'system-login':
        case 'system-advanced':
          const systemRepo = AppDataSource.getRepository(ConfigSystem);
          // 查找第一条记录，如果不存在则创建
          let systemConfig = await systemRepo.findOne({
            where: {},
            order: { id: 'ASC' }
          });

          if (!systemConfig) {
            systemConfig = systemRepo.create({
              siteName: 'AI Agent Admin',
              siteLogo: '',
              mainLogo: '',
              sidebarLogo: '',
              miniLogo: '',
              userLogo: '',
              favicon: '',
              customerServiceImage: '',
              siteDescription: '专业的AI智能体管理平台',
              copyright: '© 2023 AI Agent Admin',
              contactEmail: '<EMAIL>',
              beianInfo: '',
              siteUrl: '',
              maxLoginAttempts: 5,
              loginLockTime: 30,
              sessionTimeout: 120,
              maintenanceMode: false,
              primaryColor: '#1890ff'
            });
          }

          // 根据不同的子类型更新相应的字段
          switch (configType) {
            case 'system-basic':
              if (configValue.siteName !== undefined) systemConfig.siteName = configValue.siteName;
              if (configValue.siteDescription !== undefined) systemConfig.siteDescription = configValue.siteDescription;
              if (configValue.adminEmail !== undefined) systemConfig.contactEmail = configValue.adminEmail;
              if (configValue.copyright !== undefined) systemConfig.copyright = configValue.copyright;
              if (configValue.beianInfo !== undefined) systemConfig.beianInfo = configValue.beianInfo;
              if (configValue.siteUrl !== undefined) systemConfig.siteUrl = configValue.siteUrl;
              break;
            case 'system-logo':
              if (configValue.siteLogo !== undefined) systemConfig.siteLogo = configValue.siteLogo;
              if (configValue.mainLogo !== undefined) systemConfig.mainLogo = configValue.mainLogo;
              if (configValue.sidebarLogo !== undefined) systemConfig.sidebarLogo = configValue.sidebarLogo;
              if (configValue.miniLogo !== undefined) systemConfig.miniLogo = configValue.miniLogo;
              if (configValue.userLogo !== undefined) systemConfig.userLogo = configValue.userLogo;
              if (configValue.favicon !== undefined) systemConfig.favicon = configValue.favicon;
              if (configValue.customerServiceImage !== undefined) systemConfig.customerServiceImage = configValue.customerServiceImage;
              if (configValue.primaryColor !== undefined) systemConfig.primaryColor = configValue.primaryColor;
              break;
            case 'system-login':
              if (configValue.maxLoginAttempts !== undefined) systemConfig.maxLoginAttempts = configValue.maxLoginAttempts;
              if (configValue.loginLockTime !== undefined) systemConfig.loginLockTime = configValue.loginLockTime;
              if (configValue.sessionTimeout !== undefined) systemConfig.sessionTimeout = configValue.sessionTimeout;
              break;
            case 'system-advanced':
              if (configValue.maintenanceMode !== undefined) systemConfig.maintenanceMode = configValue.maintenanceMode;
              break;
            default: // 'system'
              // 更新所有字段
              if (configValue.siteName !== undefined) systemConfig.siteName = configValue.siteName;
              if (configValue.siteLogo !== undefined) systemConfig.siteLogo = configValue.siteLogo;
              if (configValue.mainLogo !== undefined) systemConfig.mainLogo = configValue.mainLogo;
              if (configValue.sidebarLogo !== undefined) systemConfig.sidebarLogo = configValue.sidebarLogo;
              if (configValue.miniLogo !== undefined) systemConfig.miniLogo = configValue.miniLogo;
              if (configValue.userLogo !== undefined) systemConfig.userLogo = configValue.userLogo;
              if (configValue.favicon !== undefined) systemConfig.favicon = configValue.favicon;
              if (configValue.customerServiceImage !== undefined) systemConfig.customerServiceImage = configValue.customerServiceImage;
              if (configValue.siteDescription !== undefined) systemConfig.siteDescription = configValue.siteDescription;
              if (configValue.copyright !== undefined) systemConfig.copyright = configValue.copyright;
              if (configValue.contactEmail !== undefined) systemConfig.contactEmail = configValue.contactEmail;
              if (configValue.adminEmail !== undefined) systemConfig.contactEmail = configValue.adminEmail;
              if (configValue.beianInfo !== undefined) systemConfig.beianInfo = configValue.beianInfo;
              if (configValue.siteUrl !== undefined) systemConfig.siteUrl = configValue.siteUrl;
              if (configValue.maxLoginAttempts !== undefined) systemConfig.maxLoginAttempts = configValue.maxLoginAttempts;
              if (configValue.loginLockTime !== undefined) systemConfig.loginLockTime = configValue.loginLockTime;
              if (configValue.sessionTimeout !== undefined) systemConfig.sessionTimeout = configValue.sessionTimeout;
              if (configValue.maintenanceMode !== undefined) systemConfig.maintenanceMode = configValue.maintenanceMode;
              if (configValue.primaryColor !== undefined) systemConfig.primaryColor = configValue.primaryColor;
              break;
          }

          await systemRepo.save(systemConfig);
          return true;

        case 'referral':
          const referralRepo = AppDataSource.getRepository(ConfigReferral);
          let referralConfig = await referralRepo.findOne({
            where: {},
            order: { id: 'ASC' }
          });

          if (referralConfig) {
            // 更新现有配置
            if (configValue.enabled !== undefined) referralConfig.enabled = configValue.enabled;
            if (configValue.posterBackgroundImage !== undefined) referralConfig.posterBackgroundImage = configValue.posterBackgroundImage;
            if (configValue.posterQrCodeSize !== undefined) referralConfig.posterQrCodeSize = configValue.posterQrCodeSize;
            if (configValue.posterQrCodePositionX !== undefined) referralConfig.posterQrCodePositionX = configValue.posterQrCodePositionX;
            if (configValue.posterQrCodePositionY !== undefined) referralConfig.posterQrCodePositionY = configValue.posterQrCodePositionY;
            if (configValue.shareTitle !== undefined) referralConfig.shareTitle = configValue.shareTitle;
            if (configValue.shareDescription !== undefined) referralConfig.shareDescription = configValue.shareDescription;
            if (configValue.inviteRewardPoints !== undefined) referralConfig.inviteRewardPoints = configValue.inviteRewardPoints;
            if (configValue.registerRewardPoints !== undefined) referralConfig.registerRewardPoints = configValue.registerRewardPoints;
            if (configValue.inviterReward !== undefined) referralConfig.inviterReward = configValue.inviterReward;
            if (configValue.maxInviteCount !== undefined) referralConfig.maxInviteCount = configValue.maxInviteCount;
            if (configValue.rewardValidDays !== undefined) referralConfig.rewardValidDays = configValue.rewardValidDays;
            if (configValue.inviteSuccessMessage !== undefined) referralConfig.inviteSuccessMessage = configValue.inviteSuccessMessage;
            if (configValue.inviteeReward !== undefined) referralConfig.inviteeReward = configValue.inviteeReward;
            if (configValue.promoterCashback !== undefined) referralConfig.promoterCashback = configValue.promoterCashback;
            if (configValue.promoterCommission !== undefined) referralConfig.promoterCommission = configValue.promoterCommission;
            if (configValue.minWithdrawal !== undefined) referralConfig.minWithdrawal = configValue.minWithdrawal;
            if (configValue.withdrawalChannel !== undefined) referralConfig.withdrawalChannel = configValue.withdrawalChannel;
            if (configValue.withdrawalFee !== undefined) referralConfig.withdrawalFee = configValue.withdrawalFee;
          } else {
            // 创建新配置
            referralConfig = referralRepo.create({
              enabled: configValue.enabled || false,
              posterBackgroundImage: configValue.posterBackgroundImage || '',
              posterQrCodeSize: configValue.posterQrCodeSize || 202,
              posterQrCodePositionX: configValue.posterQrCodePositionX || 200,
              posterQrCodePositionY: configValue.posterQrCodePositionY || 400,
              shareTitle: configValue.shareTitle || 'AI智能体使用邀请',
              shareDescription: configValue.shareDescription || '注册即可获得额外免费使用次数，快来体验智能AI助手！',
              inviteRewardPoints: configValue.inviteRewardPoints || 100,
              registerRewardPoints: configValue.registerRewardPoints || 50,
              inviterReward: configValue.inviterReward || 50,
              maxInviteCount: configValue.maxInviteCount || 10,
              rewardValidDays: configValue.rewardValidDays || 60,
              inviteSuccessMessage: configValue.inviteSuccessMessage || '恭喜您成功邀请好友，获得{reward}次免费使用奖励！',
              inviteeReward: configValue.inviteeReward || 30,
              promoterCashback: configValue.promoterCashback || 5.00,
              promoterCommission: configValue.promoterCommission || 2.00,
              minWithdrawal: configValue.minWithdrawal || 1.00,
              withdrawalChannel: configValue.withdrawalChannel || 'alipay',
              withdrawalFee: configValue.withdrawalFee || 0.00
            });
          }

          await referralRepo.save(referralConfig);
          return true;

        case 'free-quota':
          const freeQuotaRepo = AppDataSource.getRepository(ConfigFreeQuota);
          let freeQuotaConfig = await freeQuotaRepo.findOne({
            where: {},
            order: { id: 'ASC' }
          });

          if (freeQuotaConfig) {
            // 更新现有配置
            if (configValue.enabled !== undefined) freeQuotaConfig.enabled = configValue.enabled;
            if (configValue.newUserQuota !== undefined) freeQuotaConfig.newUserQuota = configValue.newUserQuota;
            if (configValue.quotaValidDays !== undefined) freeQuotaConfig.quotaValidDays = configValue.quotaValidDays;
            if (configValue.maxConsumePerSession !== undefined) freeQuotaConfig.maxConsumePerSession = configValue.maxConsumePerSession;
            if (configValue.insufficientQuotaMessage !== undefined) freeQuotaConfig.insufficientQuotaMessage = configValue.insufficientQuotaMessage;
          } else {
            // 创建新配置
            freeQuotaConfig = freeQuotaRepo.create({
              enabled: configValue.enabled || true,
              newUserQuota: configValue.newUserQuota || 100,
              quotaValidDays: configValue.quotaValidDays || 30,
              maxConsumePerSession: configValue.maxConsumePerSession || 20,
              insufficientQuotaMessage: configValue.insufficientQuotaMessage || '您的免费使用额度已用尽，请订阅会员计划或者购买额外次数继续使用'
            });
          }

          await freeQuotaRepo.save(freeQuotaConfig);
          return true;

        case 'verification':
          const verificationRepo = AppDataSource.getRepository(ConfigVerification);
          let verificationConfig = await verificationRepo.findOne({
            where: {},
            order: { id: 'ASC' }
          });

          if (verificationConfig) {
            // 更新现有配置 - 短信配置
            if (configValue.smsProvider !== undefined) verificationConfig.smsProvider = configValue.smsProvider;
            if (configValue.smsEnabled !== undefined) verificationConfig.smsEnabled = configValue.smsEnabled;
            if (configValue.smsAliyunAccessKeyId !== undefined) verificationConfig.smsAliyunAccessKeyId = configValue.smsAliyunAccessKeyId;
            if (configValue.smsAliyunAccessKeySecret !== undefined) verificationConfig.smsAliyunAccessKeySecret = configValue.smsAliyunAccessKeySecret;
            if (configValue.smsAliyunSignName !== undefined) verificationConfig.smsAliyunSignName = configValue.smsAliyunSignName;
            if (configValue.smsAliyunTemplateCode !== undefined) verificationConfig.smsAliyunTemplateCode = configValue.smsAliyunTemplateCode;
            if (configValue.smsTencentSecretId !== undefined) verificationConfig.smsTencentSecretId = configValue.smsTencentSecretId;
            if (configValue.smsTencentSecretKey !== undefined) verificationConfig.smsTencentSecretKey = configValue.smsTencentSecretKey;
            if (configValue.smsTencentAppId !== undefined) verificationConfig.smsTencentAppId = configValue.smsTencentAppId;
            if (configValue.smsTencentSignName !== undefined) verificationConfig.smsTencentSignName = configValue.smsTencentSignName;
            if (configValue.smsTencentTemplateId !== undefined) verificationConfig.smsTencentTemplateId = configValue.smsTencentTemplateId;
            if (configValue.smsHuaweiAppKey !== undefined) verificationConfig.smsHuaweiAppKey = configValue.smsHuaweiAppKey;
            if (configValue.smsHuaweiAppSecret !== undefined) verificationConfig.smsHuaweiAppSecret = configValue.smsHuaweiAppSecret;
            if (configValue.smsHuaweiSignName !== undefined) verificationConfig.smsHuaweiSignName = configValue.smsHuaweiSignName;
            if (configValue.smsHuaweiTemplateId !== undefined) verificationConfig.smsHuaweiTemplateId = configValue.smsHuaweiTemplateId;

            // 邮箱配置
            if (configValue.emailProvider !== undefined) verificationConfig.emailProvider = configValue.emailProvider;
            if (configValue.emailEnabled !== undefined) verificationConfig.emailEnabled = configValue.emailEnabled;
            if (configValue.emailSmtpHost !== undefined) verificationConfig.emailSmtpHost = configValue.emailSmtpHost;
            if (configValue.emailSmtpPort !== undefined) verificationConfig.emailSmtpPort = configValue.emailSmtpPort;
            if (configValue.emailSmtpFromEmail !== undefined) verificationConfig.emailSmtpFromEmail = configValue.emailSmtpFromEmail;
            if (configValue.emailSmtpPassword !== undefined) verificationConfig.emailSmtpPassword = configValue.emailSmtpPassword;
            if (configValue.emailSmtpFromName !== undefined) verificationConfig.emailSmtpFromName = configValue.emailSmtpFromName;
            if (configValue.emailSmtpSsl !== undefined) verificationConfig.emailSmtpSsl = configValue.emailSmtpSsl;
            if (configValue.emailAliyunAccessKeyId !== undefined) verificationConfig.emailAliyunAccessKeyId = configValue.emailAliyunAccessKeyId;
            if (configValue.emailAliyunAccessKeySecret !== undefined) verificationConfig.emailAliyunAccessKeySecret = configValue.emailAliyunAccessKeySecret;
            if (configValue.emailAliyunFromEmail !== undefined) verificationConfig.emailAliyunFromEmail = configValue.emailAliyunFromEmail;
            if (configValue.emailAliyunFromName !== undefined) verificationConfig.emailAliyunFromName = configValue.emailAliyunFromName;
            if (configValue.emailTencentSecretId !== undefined) verificationConfig.emailTencentSecretId = configValue.emailTencentSecretId;
            if (configValue.emailTencentSecretKey !== undefined) verificationConfig.emailTencentSecretKey = configValue.emailTencentSecretKey;
            if (configValue.emailTencentFromEmail !== undefined) verificationConfig.emailTencentFromEmail = configValue.emailTencentFromEmail;
            if (configValue.emailTencentFromName !== undefined) verificationConfig.emailTencentFromName = configValue.emailTencentFromName;

            // 存储配置
            if (configValue.storageProvider !== undefined) verificationConfig.storageProvider = configValue.storageProvider;
            if (configValue.storageEnabled !== undefined) verificationConfig.storageEnabled = configValue.storageEnabled;
            if (configValue.storageAliyunAccessKeyId !== undefined) verificationConfig.storageAliyunAccessKeyId = configValue.storageAliyunAccessKeyId;
            if (configValue.storageAliyunAccessKeySecret !== undefined) verificationConfig.storageAliyunAccessKeySecret = configValue.storageAliyunAccessKeySecret;
            if (configValue.storageAliyunBucket !== undefined) verificationConfig.storageAliyunBucket = configValue.storageAliyunBucket;
            if (configValue.storageAliyunEndpoint !== undefined) verificationConfig.storageAliyunEndpoint = configValue.storageAliyunEndpoint;
            if (configValue.storageAliyunBaseUrl !== undefined) verificationConfig.storageAliyunBaseUrl = configValue.storageAliyunBaseUrl;
            if (configValue.storageTencentSecretId !== undefined) verificationConfig.storageTencentSecretId = configValue.storageTencentSecretId;
            if (configValue.storageTencentSecretKey !== undefined) verificationConfig.storageTencentSecretKey = configValue.storageTencentSecretKey;
            if (configValue.storageTencentBucket !== undefined) verificationConfig.storageTencentBucket = configValue.storageTencentBucket;
            if (configValue.storageTencentBaseUrl !== undefined) verificationConfig.storageTencentBaseUrl = configValue.storageTencentBaseUrl;
          } else {
            // 创建新配置
            verificationConfig = verificationRepo.create({
              smsProvider: configValue.smsProvider || 'aliyun',
              smsEnabled: configValue.smsEnabled || false,
              smsAliyunAccessKeyId: configValue.smsAliyunAccessKeyId || '',
              smsAliyunAccessKeySecret: configValue.smsAliyunAccessKeySecret || '',
              smsAliyunSignName: configValue.smsAliyunSignName || '',
              smsAliyunTemplateCode: configValue.smsAliyunTemplateCode || '',
              smsTencentSecretId: configValue.smsTencentSecretId || '',
              smsTencentSecretKey: configValue.smsTencentSecretKey || '',
              smsTencentAppId: configValue.smsTencentAppId || '',
              smsTencentSignName: configValue.smsTencentSignName || '',
              smsTencentTemplateId: configValue.smsTencentTemplateId || '',
              smsHuaweiAppKey: configValue.smsHuaweiAppKey || '',
              smsHuaweiAppSecret: configValue.smsHuaweiAppSecret || '',
              smsHuaweiSignName: configValue.smsHuaweiSignName || '',
              smsHuaweiTemplateId: configValue.smsHuaweiTemplateId || '',
              emailProvider: configValue.emailProvider || 'smtp',
              emailEnabled: configValue.emailEnabled || false,
              emailSmtpHost: configValue.emailSmtpHost || '',
              emailSmtpPort: configValue.emailSmtpPort || 587,
              emailSmtpFromEmail: configValue.emailSmtpFromEmail || '',
              emailSmtpPassword: configValue.emailSmtpPassword || '',
              emailSmtpFromName: configValue.emailSmtpFromName || '',
              emailSmtpSsl: configValue.emailSmtpSsl || true,
              emailAliyunAccessKeyId: configValue.emailAliyunAccessKeyId || '',
              emailAliyunAccessKeySecret: configValue.emailAliyunAccessKeySecret || '',
              emailAliyunFromEmail: configValue.emailAliyunFromEmail || '',
              emailAliyunFromName: configValue.emailAliyunFromName || '',
              emailTencentSecretId: configValue.emailTencentSecretId || '',
              emailTencentSecretKey: configValue.emailTencentSecretKey || '',
              emailTencentFromEmail: configValue.emailTencentFromEmail || '',
              emailTencentFromName: configValue.emailTencentFromName || '',
              storageProvider: configValue.storageProvider || 'aliyun',
              storageEnabled: configValue.storageEnabled || false,
              storageAliyunAccessKeyId: configValue.storageAliyunAccessKeyId || '',
              storageAliyunAccessKeySecret: configValue.storageAliyunAccessKeySecret || '',
              storageAliyunBucket: configValue.storageAliyunBucket || '',
              storageAliyunEndpoint: configValue.storageAliyunEndpoint || '',
              storageAliyunBaseUrl: configValue.storageAliyunBaseUrl || '',
              storageTencentSecretId: configValue.storageTencentSecretId || '',
              storageTencentSecretKey: configValue.storageTencentSecretKey || '',
              storageTencentBucket: configValue.storageTencentBucket || '',
              storageTencentBaseUrl: configValue.storageTencentBaseUrl || ''
            });
          }

          await verificationRepo.save(verificationConfig);
          return true;

        default:
          return false;
      }
    } catch (error) {
      console.error(`保存新配置表数据失败 (${configType}):`, error);
      return false;
    }
  }

  /**
   * 前端兼容 - 保存指定类型的配置（前端格式）
   * @param req - 请求对象
   * @param res - 响应对象
   */
  async saveFrontendConfigByType(req: Request, res: Response) {
    const { configType } = req.params;
    const configValue = req.body;

    if (!configType) {
      return res.status(400).json({
        code: 400,
        message: '配置类型不能为空'
      });
    }

    try {
      // 检查配置类型是否支持
      const supportedTypes = [
        'wechat-official', 'wechat-miniprogram', 'wechat-pay', 'alipay',
        'kouzi', 'verification', 'free-quota', 'referral',
        'system', 'system-basic', 'system-logo', 'system-login', 'system-advanced'
      ];

      if (!supportedTypes.includes(configType)) {
        return res.status(400).json({
          code: 400,
          message: `不支持的配置类型: ${configType}`,
          data: null
        });
      }

      // 尝试保存到新的独立配置表
      const savedToNewTable = await this.saveToNewConfigTable(configType, configValue);

      if (savedToNewTable) {
        console.log(`✅ 配置已保存到新的独立表: ${configType}`);
        return res.status(200).json({
          code: 200,
          message: '配置保存成功',
          data: {
            configType,
            configValue,
            savedToNewTable: true,
            lastUpdated: new Date()
          }
        });
      }

      // 如果保存失败，返回错误
      return res.status(500).json({
        code: 500,
        message: `保存配置失败: ${configType}`,
        data: null
      });
    } catch (error) {
      console.error('保存配置失败:', error);
      return res.status(500).json({
        code: 500,
        message: '保存配置失败',
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 根据配置类型获取对应的实体类
   */
  private getEntityByType(configType: string): any {
    switch (configType) {
      case 'wechat-official':
        return ConfigWechatOfficial;
      case 'wechat-miniprogram':
        return ConfigWechatMiniprogram;
      case 'wechat-pay':
        return ConfigWechatPay;
      case 'alipay':
        return ConfigAlipay;
      case 'kouzi':
        return ConfigKouzi;
      case 'verification':
        return ConfigVerification;
      case 'free-quota':
        return ConfigFreeQuota;
      case 'referral':
        return ConfigReferral;
      case 'system':
        return ConfigSystem;
      default:
        return null;
    }
  }

  // 获取所有配置状态
  async getAllConfigStatus(req: Request, res: Response) {
    try {
      const configTypes = ['wechat-official', 'wechat-miniprogram', 'wechat-pay', 'alipay', 'kouzi', 'verification', 'free-quota', 'referral', 'system'];
      const configList = [];

      for (const configType of configTypes) {
        try {
          const entity = this.getEntityByType(configType);
          if (!entity) continue;

          const repository = AppDataSource.getRepository(entity);
          const configs = await repository.find();

          const configValue: any = {};
          configs.forEach((config: any) => {
            configValue[config.key] = config.value;
          });

          configList.push({
            configType,
            description: this.getConfigDescription(configType),
            isConfigured: configs.length > 0,
            isActive: true,
            lastTestResult: null,
            lastTestTime: null,
            configValue
          });
        } catch (error) {
          console.error(`获取${configType}配置状态失败:`, error);
        }
      }

      res.json({
        code: 200,
        message: '获取配置状态成功',
        data: configList
      });
    } catch (error: any) {
      console.error('获取所有配置状态失败:', error);
      res.status(500).json({
        code: 500,
        message: '获取配置状态失败',
        error: error.message
      });
    }
  }

  // 测试配置连接
  async testConfig(req: Request, res: Response) {
    try {
      const { configType } = req.params;
      const testData = req.body;

      console.log(`收到测试请求: ${configType}`);

      // 模拟测试过程 - 这里可以根据不同的配置类型实现具体的测试逻辑
      const success = true;
      const testMessage = success ? '连接测试成功' : '连接测试失败';

      res.json({
        code: 200,
        message: '测试连接请求已处理',
        data: {
          success,
          message: testMessage,
          timestamp: new Date().toISOString()
        }
      });
    } catch (error: any) {
      console.error('测试配置失败:', error);
      res.status(500).json({
        code: 500,
        message: '测试配置失败',
        error: error.message
      });
    }
  }

  private getConfigDescription(configType: string): string {
    const descriptions: { [key: string]: string } = {
      'wechat-official': '微信公众号配置',
      'wechat-miniprogram': '微信小程序配置',
      'wechat-pay': '微信支付配置',
      'alipay': '支付宝配置',
      'kouzi': '扣子配置',
      'verification': '验证配置',
      'free-quota': '免费额度配置',
      'referral': '推荐配置',
      'system': '系统配置'
    };
    return descriptions[configType] || `${configType}配置`;
  }
}
