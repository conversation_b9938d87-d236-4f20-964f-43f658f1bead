import { AppDataSource } from '../data-source';
import { DailyLimitService } from '../services/daily-limit.service';

async function testDailyLimitPoints() {
  console.log('🧪 开始测试基于点数的每日限制系统...\n');

  try {
    // 初始化数据库连接
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
      console.log('✅ 数据库连接已建立\n');
    }

    // 测试用户ID
    const testUserId = 60; // 使用现有免费用户进行测试
    const vipUserId = 59; // 使用现有VIP用户进行测试

    console.log('=== 测试场景 1: 免费用户每日限制检查 ===');
    
    // 1. 检查用户当前状态
    console.log('1. 检查用户当前状态...');
    const userInfo = await AppDataSource.query(`
      SELECT id, name, points, packageId, packageExpiredAt 
      FROM members 
      WHERE id = ?
    `, [testUserId]);
    
    if (userInfo.length === 0) {
      console.log('❌ 测试用户不存在，请先创建测试用户');
      return;
    }
    
    console.log('用户信息:', {
      id: userInfo[0].id,
      name: userInfo[0].name,
      points: userInfo[0].points,
      packageId: userInfo[0].packageId,
      packageExpiredAt: userInfo[0].packageExpiredAt
    });

    // 2. 测试不同点数的每日限制检查
    console.log('\n2. 测试不同点数的每日限制检查...');
    
    const testCases = [
      { points: 1, description: '1点数消耗' },
      { points: 10, description: '10点数消耗' },
      { points: 100, description: '100点数消耗' },
      { points: 1000, description: '1000点数消耗' }
    ];

    for (const testCase of testCases) {
      console.log(`\n📊 测试 ${testCase.description}:`);
      try {
        const result = await DailyLimitService.checkDailyLimit(testUserId, testCase.points);
        console.log('检查结果:', {
          allowed: result.allowed,
          todayUsage: result.todayUsage,
          dailyLimit: result.dailyLimit,
          isVipUser: result.isVipUser,
          message: result.message
        });
      } catch (error) {
        console.error('检查失败:', error);
      }
    }

    // 3. 获取今日使用统计
    console.log('\n3. 获取今日使用统计...');
    try {
      const stats = await DailyLimitService.getTodayUsageStats(testUserId);
      console.log('今日使用统计:', {
        totalUsage: stats.totalUsage,
        chatUsage: stats.chatUsage,
        workflowUsage: stats.workflowUsage,
        dailyLimit: stats.dailyLimit,
        remaining: stats.remaining,
        isVipUser: stats.isVipUser
      });
    } catch (error) {
      console.error('获取统计失败:', error);
    }

    // 4. 测试VIP用户
    console.log('\n=== 测试场景 2: VIP用户每日限制检查 ===');

    // 获取VIP用户信息
    const vipUserInfo = await AppDataSource.query(`
      SELECT id, name, points, packageId, packageExpiredAt
      FROM members
      WHERE id = ?
    `, [vipUserId]);

    if (vipUserInfo.length > 0) {
      console.log('VIP用户信息:', {
        id: vipUserInfo[0].id,
        name: vipUserInfo[0].name,
        points: vipUserInfo[0].points,
        packageId: vipUserInfo[0].packageId,
        packageExpiredAt: vipUserInfo[0].packageExpiredAt
      });

      // 获取套餐信息
      if (vipUserInfo[0].packageId) {
        const packageInfo = await AppDataSource.query(`
          SELECT id, title, dailyMaxConsumption, totalQuota
          FROM package
          WHERE id = ?
        `, [vipUserInfo[0].packageId]);

        if (packageInfo.length > 0) {
          console.log('套餐信息:', {
            id: packageInfo[0].id,
            title: packageInfo[0].title,
            dailyMaxConsumption: packageInfo[0].dailyMaxConsumption,
            totalQuota: packageInfo[0].totalQuota
          });

          // 测试套餐用户的限制
          const vipResult = await DailyLimitService.checkDailyLimit(vipUserId, 500);
          console.log('VIP用户检查结果:', {
            allowed: vipResult.allowed,
            todayUsage: vipResult.todayUsage,
            dailyLimit: vipResult.dailyLimit,
            isVipUser: vipResult.isVipUser,
            message: vipResult.message
          });

          // 测试VIP用户统计
          const vipStats = await DailyLimitService.getTodayUsageStats(vipUserId);
          console.log('VIP用户今日统计:', {
            totalUsage: vipStats.totalUsage,
            dailyLimit: vipStats.dailyLimit,
            remaining: vipStats.remaining,
            isVipUser: vipStats.isVipUser
          });
        }
      }
    }

    console.log('\n✅ 测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    // 关闭数据库连接
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 运行测试
testDailyLimitPoints().catch(console.error);
