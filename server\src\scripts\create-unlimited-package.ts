import { AppDataSource } from '../data-source';

async function createUnlimitedPackage() {
  try {
    await AppDataSource.initialize();
    console.log('✅ 数据库连接已建立');

    // 1. 查找现有套餐并修改为无限制
    const existingPackages = await AppDataSource.query(`
      SELECT id, title, dailyMaxConsumption FROM package LIMIT 5
    `);
    console.log('现有套餐:', existingPackages);

    // 2. 使用现有的无限制套餐（ID 17）
    console.log('✅ 使用现有的无限制套餐 ID 17');

    // 3. 更新用户61为无限制套餐
    await AppDataSource.query(`
      UPDATE members
      SET packageId = 17, packageExpiredAt = DATE_ADD(NOW(), INTERVAL 30 DAY)
      WHERE id = 61
    `);
    
    console.log('✅ 用户61已设置为无限制套餐');
    
    // 3. 验证设置
    const user = await AppDataSource.query(`
      SELECT m.id, m.name, m.points, p.title, p.dailyMaxConsumption, m.packageExpiredAt
      FROM members m
      LEFT JOIN package p ON m.packageId = p.id
      WHERE m.id = 61
    `);
    
    console.log('📊 用户信息:', user[0]);
    
    await AppDataSource.destroy();
    console.log('🔌 数据库连接已关闭');
    
  } catch (error) {
    console.error('❌ 创建失败:', error);
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
    }
  }
}

createUnlimitedPackage();
