const mysql = require('mysql2/promise');

async function checkReferralData() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'root',
    database: 'ai_agent'
  });

  try {
    // 查询推荐配置数据
    const [rows] = await connection.execute('SELECT * FROM config_referral WHERE id = 1');
    
    if (rows.length > 0) {
      console.log('当前推荐配置数据:');
      console.log(JSON.stringify(rows[0], null, 2));
    } else {
      console.log('没有找到推荐配置数据');
    }

  } catch (error) {
    console.error('❌ 查询失败:', error.message);
  } finally {
    await connection.end();
  }
}

checkReferralData();
