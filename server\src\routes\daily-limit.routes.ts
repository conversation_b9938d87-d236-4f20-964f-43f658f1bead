import { Router } from 'express';
import { DailyLimitService } from '../services/daily-limit.service';
import { authMiddleware } from '../middleware/auth.middleware';

const router = Router();

/**
 * 获取用户分类额度详情
 * GET /api/daily-limit/quota-details
 */
router.get('/quota-details', authMiddleware, async (req, res) => {
  try {
    const userId = (req as any).user?.id;

    if (!userId) {
      return res.status(401).json({
        code: 401,
        message: '用户未登录'
      });
    }

    console.log(`📊 获取用户 ${userId} 的分类额度详情`);

    // 获取用户分类额度详情
    const quotaDetails = await DailyLimitService.getQuotaDetailsByCategory(userId);

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: quotaDetails
    });

  } catch (error) {
    console.error('获取分类额度详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    });
  }
});

/**
 * 获取用户每日使用统计 (保持向后兼容)
 * GET /api/daily-limit/stats
 */
router.get('/stats', authMiddleware, async (req, res) => {
  try {
    const userId = (req as any).user?.id;

    if (!userId) {
      return res.status(401).json({
        code: 401,
        message: '用户未登录'
      });
    }

    console.log(`📊 获取用户 ${userId} 的每日使用统计`);

    // 获取用户今日使用统计
    const stats = await DailyLimitService.getTodayUsageStats(userId);

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: stats
    });

  } catch (error) {
    console.error('获取每日使用统计失败:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    });
  }
});

/**
 * 检查用户每日限制
 * POST /api/daily-limit/check
 */
router.post('/check', authMiddleware, async (req, res) => {
  try {
    const userId = (req as any).user?.id;
    const { requiredPoints = 1 } = req.body;
    
    if (!userId) {
      return res.status(401).json({
        code: 401,
        message: '用户未登录'
      });
    }

    console.log(`🔍 检查用户 ${userId} 的每日限制，需要点数: ${requiredPoints}`);

    // 检查每日限制
    const result = await DailyLimitService.checkDailyLimit(userId, requiredPoints);

    if (!result.allowed) {
      return res.status(429).json({
        code: 429,
        message: result.message || '每日使用限制已达上限',
        data: {
          todayUsage: result.todayUsage,
          dailyLimit: result.dailyLimit,
          isVipUser: result.isVipUser
        }
      });
    }

    return res.status(200).json({
      code: 200,
      message: '检查通过',
      data: {
        allowed: true,
        todayUsage: result.todayUsage,
        dailyLimit: result.dailyLimit,
        isVipUser: result.isVipUser
      }
    });

  } catch (error) {
    console.error('检查每日限制失败:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    });
  }
});

export default router;
