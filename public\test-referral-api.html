<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>推广配置API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>推广配置API测试</h1>
        
        <div class="test-section">
            <h3>1. 测试推广配置API</h3>
            <button onclick="testReferralConfig()">获取推广配置</button>
            <div id="referral-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>2. 测试生成邀请码API</h3>
            <button onclick="testGenerateInviteCode()">生成邀请码</button>
            <div id="invite-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>3. 测试用户信息API</h3>
            <button onclick="testUserInfo()">获取用户信息</button>
            <div id="user-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>4. 综合测试</h3>
            <button onclick="runAllTests()">运行所有测试</button>
            <div id="all-result" class="result"></div>
        </div>
    </div>

    <script>
        function log(message, elementId = 'all-result', type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}\n`;
            
            if (element.textContent === '') {
                element.textContent = logMessage;
            } else {
                element.textContent += logMessage;
            }
            
            element.className = `result ${type}`;
            console.log(message);
        }

        async function testReferralConfig() {
            const resultDiv = document.getElementById('referral-result');
            resultDiv.textContent = '测试中...';
            resultDiv.className = 'result info';
            
            try {
                const response = await fetch('/api/config/referral');
                const data = await response.json();
                
                if (response.ok && data.success) {
                    resultDiv.textContent = `✓ 推广配置API调用成功\n${JSON.stringify(data, null, 2)}`;
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.textContent = `✗ 推广配置API调用失败: ${data.message || '未知错误'}`;
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.textContent = `✗ 请求失败: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        async function testGenerateInviteCode() {
            const resultDiv = document.getElementById('invite-result');
            resultDiv.textContent = '测试中...';
            resultDiv.className = 'result info';
            
            try {
                // 模拟token（实际使用中应该从localStorage获取）
                const token = localStorage.getItem('token') || 'demo-token';
                
                const response = await fetch('/api/config/generate-invite-code', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({})
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    resultDiv.textContent = `✓ 邀请码生成成功\n${JSON.stringify(data, null, 2)}`;
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.textContent = `✗ 邀请码生成失败: ${data.message || '未知错误'}`;
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.textContent = `✗ 请求失败: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        async function testUserInfo() {
            const resultDiv = document.getElementById('user-result');
            resultDiv.textContent = '测试中...';
            resultDiv.className = 'result info';
            
            try {
                const token = localStorage.getItem('token') || 'demo-token';
                
                const response = await fetch('/api/users/me', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const data = await response.json();
                
                if (response.ok && data.code === 200) {
                    resultDiv.textContent = `✓ 用户信息获取成功\n${JSON.stringify(data, null, 2)}`;
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.textContent = `✗ 用户信息获取失败: ${data.message || '未知错误'}`;
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.textContent = `✗ 请求失败: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        async function runAllTests() {
            const resultDiv = document.getElementById('all-result');
            resultDiv.textContent = '';
            resultDiv.className = 'result info';
            
            log('开始运行所有测试...');
            
            // 测试1: 推广配置
            log('测试1: 推广配置API');
            try {
                const response = await fetch('/api/config/referral');
                const data = await response.json();
                if (response.ok && data.success) {
                    log('✓ 推广配置API - 成功');
                } else {
                    log(`✗ 推广配置API - 失败: ${data.message}`);
                }
            } catch (error) {
                log(`✗ 推广配置API - 错误: ${error.message}`);
            }
            
            // 测试2: 生成邀请码
            log('测试2: 生成邀请码API');
            try {
                const token = localStorage.getItem('token') || 'demo-token';
                const response = await fetch('/api/config/generate-invite-code', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({})
                });
                const data = await response.json();
                if (response.ok && data.success) {
                    log('✓ 生成邀请码API - 成功');
                } else {
                    log(`✗ 生成邀请码API - 失败: ${data.message}`);
                }
            } catch (error) {
                log(`✗ 生成邀请码API - 错误: ${error.message}`);
            }
            
            log('所有测试完成');
        }

        // 页面加载时自动运行测试
        window.onload = function() {
            log('页面加载完成，可以开始测试');
        };
    </script>
</body>
</html>
