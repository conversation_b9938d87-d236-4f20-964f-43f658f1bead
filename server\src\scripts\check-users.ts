import { AppDataSource } from '../data-source';

async function checkUsers() {
  console.log('🔍 检查数据库中的用户...\n');

  try {
    // 初始化数据库连接
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
      console.log('✅ 数据库连接已建立\n');
    }

    // 查询所有用户
    const users = await AppDataSource.query(`
      SELECT id, name, phone, email, points, packageId, packageExpiredAt, createdAt
      FROM members 
      ORDER BY id ASC
      LIMIT 10
    `);

    console.log(`📊 找到 ${users.length} 个用户:`);
    users.forEach((user: any, index: number) => {
      console.log(`${index + 1}. 用户ID: ${user.id}, 姓名: ${user.name}, 手机: ${user.phone}, 点数: ${user.points}, 套餐ID: ${user.packageId}`);
    });

    // 查询套餐信息
    console.log('\n📦 套餐信息:');
    const packages = await AppDataSource.query(`
      SELECT id, title, dailyMaxConsumption, totalQuota, enabled
      FROM package
      WHERE enabled = 1
      ORDER BY id ASC
      LIMIT 5
    `);

    packages.forEach((pkg: any, index: number) => {
      console.log(`${index + 1}. 套餐ID: ${pkg.id}, 名称: ${pkg.title}, 每日限制: ${pkg.dailyMaxConsumption}, 总额度: ${pkg.totalQuota}`);
    });

    // 查询免费额度配置
    console.log('\n⚙️ 免费额度配置:');
    const configs = await AppDataSource.query(`
      SELECT enabled, dailyLimit, totalLimit, resetType
      FROM config_free_quota
      WHERE id = 1
    `);

    if (configs.length > 0) {
      const config = configs[0];
      console.log(`启用状态: ${config.enabled}`);
      console.log(`每日限制: ${config.dailyLimit}`);
      console.log(`总限制: ${config.totalLimit}`);
      console.log(`重置类型: ${config.resetType}`);
    } else {
      console.log('未找到免费额度配置');
    }

  } catch (error) {
    console.error('❌ 检查失败:', error);
  } finally {
    // 关闭数据库连接
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('\n🔌 数据库连接已关闭');
    }
  }
}

// 运行检查
checkUsers().catch(console.error);
