<template>
  <div class="system-logo-page">
    <n-card title="Logo和品牌设置" class="config-card">
      <template #header-extra>
        <n-tag type="info" size="small">
          <template #icon>
            <n-icon><picture-outlined /></n-icon>
          </template>
          品牌配置
        </n-tag>
      </template>
      
      <div class="config-description">
        配置系统的各种Logo、图标和主题色，包括侧边栏Logo、用户头像、网站图标、客服图片等。
      </div>
      
      <!-- 使用现有的SystemLogoConfig组件 -->
      <system-logo-config @saved="handleConfigSaved" />
    </n-card>
  </div>
</template>

<script setup>
import { NCard, NTag, NIcon, useMessage } from 'naive-ui';
import { PictureOutlined } from '@vicons/antd';
import SystemLogoConfig from '../system/components/SystemLogoConfig.vue';

const emit = defineEmits(['saved']);
const message = useMessage();

// 处理配置保存成功
function handleConfigSaved(configType) {
  console.log('Logo配置保存成功:', configType);
  message.success('Logo配置保存成功');
  
  // 通知父组件
  emit('saved', configType);
  
  // 触发全局事件
  window.dispatchEvent(new CustomEvent('system-logo-config-saved', {
    detail: { type: 'system-logo' }
  }));
}
</script>

<style scoped>
.system-logo-page {
  padding: 0;
}

.config-card {
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);
}

.config-card :deep(.n-card__header) {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.config-card :deep(.n-card__content) {
  padding: 20px;
}

.config-description {
  color: #666;
  font-size: 14px;
  margin-bottom: 20px;
  line-height: 1.6;
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #52C41A;
}
</style>
