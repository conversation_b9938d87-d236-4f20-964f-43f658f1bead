import { AppDataSource } from './data-source';

async function addLogoFields() {
  try {
    console.log('初始化数据源...');
    await AppDataSource.initialize();
    
    console.log('检查config_system表结构...');
    
    // 检查表是否存在以及当前字段
    const tableExists = await AppDataSource.query(`
      SELECT COUNT(*) as count 
      FROM information_schema.tables 
      WHERE table_schema = 'ai_agent' AND table_name = 'config_system'
    `);
    
    if (tableExists[0].count === 0) {
      console.log('config_system表不存在，跳过添加字段');
      process.exit(0);
    }
    
    // 检查字段是否已存在
    const columns = await AppDataSource.query(`
      SELECT COLUMN_NAME 
      FROM information_schema.columns 
      WHERE table_schema = 'ai_agent' AND table_name = 'config_system'
    `);
    
    const existingColumns = columns.map((col: any) => col.COLUMN_NAME);
    console.log('现有字段:', existingColumns);
    
    const fieldsToAdd = [
      { name: 'main_logo', sql: 'ADD COLUMN `main_logo` VARCHAR(500) NOT NULL DEFAULT \'\' COMMENT \'主Logo图片URL\' AFTER `site_logo`' },
      { name: 'sidebar_logo', sql: 'ADD COLUMN `sidebar_logo` VARCHAR(500) NOT NULL DEFAULT \'\' COMMENT \'侧边栏Logo图片URL\' AFTER `main_logo`' },
      { name: 'mini_logo', sql: 'ADD COLUMN `mini_logo` VARCHAR(500) NOT NULL DEFAULT \'\' COMMENT \'迷你Logo图片URL\' AFTER `sidebar_logo`' },
      { name: 'user_logo', sql: 'ADD COLUMN `user_logo` VARCHAR(500) NOT NULL DEFAULT \'\' COMMENT \'用户Logo图片URL\' AFTER `mini_logo`' },
      { name: 'favicon', sql: 'ADD COLUMN `favicon` VARCHAR(500) NOT NULL DEFAULT \'\' COMMENT \'网站图标URL\' AFTER `user_logo`' },
      { name: 'customer_service_image', sql: 'ADD COLUMN `customer_service_image` VARCHAR(500) NOT NULL DEFAULT \'\' COMMENT \'客服图片URL\' AFTER `favicon`' }
    ];
    
    for (const field of fieldsToAdd) {
      if (!existingColumns.includes(field.name)) {
        console.log(`添加字段: ${field.name}`);
        try {
          await AppDataSource.query(`ALTER TABLE \`config_system\` ${field.sql}`);
          console.log(`✅ 字段 ${field.name} 添加成功`);
        } catch (error: any) {
          console.error(`❌ 添加字段 ${field.name} 失败:`, error.message);
        }
      } else {
        console.log(`⚠️ 字段 ${field.name} 已存在，跳过`);
      }
    }
    
    console.log('Logo字段添加完成！');
    process.exit(0);
  } catch (error) {
    console.error('添加Logo字段失败:', error);
    process.exit(1);
  }
}

addLogoFields();
