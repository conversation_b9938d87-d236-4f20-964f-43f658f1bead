/**
 * 微信公众号配置实体类
 */
import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('config_wechat_official')
export class ConfigWechatOfficial {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'tinyint', width: 1, default: 0, comment: '是否启用' })
  enabled: boolean;

  @Column({ name: 'app_id', length: 100, default: '', comment: '微信公众号AppID' })
  appId: string;

  @Column({ name: 'app_secret', length: 255, default: '', comment: '微信公众号AppSecret' })
  appSecret: string;

  @Column({ length: 100, default: '', comment: '微信公众号Token' })
  token: string;

  @Column({ name: 'encoding_aes_key', length: 255, default: '', comment: '消息加解密密钥' })
  encodingAesKey: string;

  @Column({ name: 'verify_file_name', length: 255, default: '', comment: '域名验证文件名' })
  verifyFileName: string;

  @Column({ name: 'verify_file_content', type: 'text', comment: '域名验证文件内容' })
  verifyFileContent: string;

  @CreateDateColumn({ name: 'created_at', comment: '创建时间' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', comment: '更新时间' })
  updatedAt: Date;
}
