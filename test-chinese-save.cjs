const mysql = require('mysql2/promise');

async function testChineseSave() {
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'root',
      database: 'ai_agent',
      charset: 'utf8mb4'
    });

    console.log('连接到数据库成功');

    // 测试更新中文字符
    console.log('测试保存中文字符...');
    await connection.execute(`
      UPDATE config_system 
      SET site_name = ?, site_description = ?, updated_at = NOW()
      WHERE id = 1
    `, ['AI智能体管理系统', '这是一个专业的AI智能体管理平台，支持多种配置和管理功能']);

    // 读取更新后的数据
    console.log('读取更新后的数据...');
    const [rows] = await connection.execute('SELECT * FROM config_system WHERE id = 1');
    
    console.log('更新后的数据:');
    console.log(rows[0]);

    await connection.end();
    console.log('测试完成');
  } catch (error) {
    console.error('测试失败:', error);
  }
}

testChineseSave();
