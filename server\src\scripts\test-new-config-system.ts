import { AppDataSource } from '../data-source';
import { ConfigWechatOfficial } from '../entity/ConfigWechatOfficial';
import { ConfigWechatMiniprogram } from '../entity/ConfigWechatMiniprogram';
import { ConfigWechatPay } from '../entity/ConfigWechatPay';
import { ConfigAlipay } from '../entity/ConfigAlipay';
import { Config<PERSON>ou<PERSON> } from '../entity/ConfigKouzi';
import { ConfigVerification } from '../entity/ConfigVerification';
import { ConfigFreeQuota } from '../entity/ConfigFreeQuota';
import { ConfigReferral } from '../entity/ConfigReferral';
import { ConfigSystem } from '../entity/ConfigSystem';

async function testNewConfigSystem() {
  try {
    console.log('🔄 初始化数据库连接...');
    await AppDataSource.initialize();
    console.log('✅ 数据库连接成功\n');

    // 测试所有新配置表
    const configTests = [
      {
        name: '微信公众号配置',
        entity: ConfigWechatOfficial,
        testData: {
          enabled: true,
          appId: 'test_app_id',
          appSecret: 'test_app_secret',
          token: 'test_token',
          encodingAesKey: 'test_aes_key'
        }
      },
      {
        name: '微信小程序配置',
        entity: ConfigWechatMiniprogram,
        testData: {
          enabled: true,
          appId: 'test_mini_app_id',
          appSecret: 'test_mini_app_secret',
          bannerAdEnabled: true,
          interstitialAdEnabled: false,
          videoAdEnabled: true
        }
      },
      {
        name: '微信支付配置',
        entity: ConfigWechatPay,
        testData: {
          enabled: true,
          mchId: 'test_mch_id',
          apiKey: 'test_api_key',
          apiV3Key: 'test_api_v3_key',
          serialNo: 'test_serial_no',
          sandbox: false
        }
      },
      {
        name: '支付宝配置',
        entity: ConfigAlipay,
        testData: {
          enabled: true,
          appId: 'test_alipay_app_id',
          privateKey: 'test_private_key',
          alipayPublicKey: 'test_alipay_public_key',
          signType: 'RSA2',
          notifyUrl: 'https://example.com/notify',
          sandboxMode: false
        }
      },
      {
        name: '扣子配置',
        entity: ConfigKouzi,
        testData: {
          enabled: true,
          appId: 'test_kouzi_app_id',
          appSecret: 'test_kouzi_app_secret',
          privateKey: 'test_private_key',
          publicKey: 'test_public_key',
          personalAccessToken: 'test_token',
          authMode: 'oauth'
        }
      },
      {
        name: '验证配置',
        entity: ConfigVerification,
        testData: {
          enabled: true,
          emailEnabled: true,
          smsEnabled: false,
          emailProvider: 'smtp',
          smtpHost: 'smtp.example.com',
          smtpPort: 587,
          smtpUser: '<EMAIL>',
          smtpPassword: 'test_password',
          smtpSecure: true
        }
      },
      {
        name: '免费额度配置',
        entity: ConfigFreeQuota,
        testData: {
          enabled: true,
          dailyLimit: 100,
          totalLimit: 1000,
          resetType: 'daily'
        }
      },
      {
        name: '推荐奖励配置',
        entity: ConfigReferral,
        testData: {
          enabled: true,
          referrerReward: 50,
          refereeReward: 20,
          maxReferrals: 10,
          rewardType: 'points'
        }
      },
      {
        name: '系统配置',
        entity: ConfigSystem,
        testData: {
          enabled: true,
          siteName: '测试AI系统',
          siteUrl: 'https://test.example.com',
          adminEmail: '<EMAIL>',
          enableRegistration: true,
          enableEmailVerification: false
        }
      }
    ];

    console.log('🧪 开始测试新配置系统...\n');

    for (const test of configTests) {
      console.log(`📋 测试 ${test.name}...`);
      
      try {
        const repository = AppDataSource.getRepository(test.entity);
        
        // 检查是否已存在配置
        let config = await repository.findOne({ where: { id: 1 } });
        
        if (config) {
          console.log(`  ✅ 找到现有配置`);
          // 更新配置
          Object.assign(config, test.testData);
          await repository.save(config);
          console.log(`  ✅ 配置更新成功`);
        } else {
          console.log(`  ℹ️  未找到现有配置，创建新配置`);
          // 创建新配置
          config = repository.create(test.testData);
          await repository.save(config);
          console.log(`  ✅ 配置创建成功`);
        }
        
        // 验证配置
        const savedConfig = await repository.findOne({ where: { id: 1 } });
        if (savedConfig) {
          console.log(`  ✅ 配置验证成功`);
        } else {
          console.log(`  ❌ 配置验证失败`);
        }
        
      } catch (error) {
        console.log(`  ❌ 测试失败:`, error instanceof Error ? error.message : String(error));
      }
      
      console.log('');
    }

    console.log('🎉 新配置系统测试完成！');
    
    // 显示统计信息
    console.log('\n📊 配置表统计:');
    for (const test of configTests) {
      try {
        const repository = AppDataSource.getRepository(test.entity);
        const count = await repository.count();
        console.log(`  ${test.name}: ${count} 条记录`);
      } catch (error) {
        console.log(`  ${test.name}: 查询失败`);
      }
    }

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('\n🔌 数据库连接已关闭');
    }
  }
}

// 运行测试
testNewConfigSystem().catch(console.error);
