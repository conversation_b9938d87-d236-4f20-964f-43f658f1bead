/**
 * 配置控制器 - 处理旧的system_config表相关的配置操作
 * 主要用于兼容性和特定的配置功能
 */

import { Request, Response } from 'express';
import { getRepository } from 'typeorm';
import { Config } from '../entity/Config';
import { ConfigChangeLog } from '../entity/ConfigChangeLog';
import { AppDataSource } from '../data-source';
import { Member } from '../entity/Member';

/**
 * 获取指定类型的配置
 */
export const getConfigByType = async (req: Request, res: Response) => {
  try {
    const { type } = req.params;
    
    if (!type) {
      return res.status(400).json({
        success: false,
        message: '配置类型不能为空'
      });
    }
    
    const configRepo = AppDataSource.getRepository(Config);
    const configs = await configRepo.find({ 
      where: { 
        type: type 
      } 
    });
    
    return res.json({
      success: true,
      data: configs
    });
  } catch (error: any) {
    console.error('获取配置失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取配置失败',
      error: error.message
    });
  }
};

/**
 * 更新配置
 */
export const updateConfig = async (req: Request, res: Response) => {
  try {
    const { type } = req.params;
    const configItems = req.body;
    
    if (!type) {
      return res.status(400).json({
        success: false,
        message: '配置类型不能为空'
      });
    }
    
    if (!Array.isArray(configItems)) {
      return res.status(400).json({
        success: false,
        message: '配置数据格式不正确，应为数组'
      });
    }
    
    const configRepo = AppDataSource.getRepository(Config);
    const logRepo = AppDataSource.getRepository(ConfigChangeLog);
    
    // 获取当前用户
    const userId = req.user?.id || 0; // 默认使用0代替null
    const userName = req.user?.username || 'system';
    
    // 处理每个配置项
    for (const item of configItems) {
      const { key, value, description } = item;
      
      if (!key) continue;
      
      // 查找现有配置
      let config = await configRepo.findOne({ 
        where: { 
          type: type, 
          key 
        } 
      });
      
      if (config) {
        // 记录变更日志
        const changeLog = new ConfigChangeLog();
        changeLog.type = type;
        changeLog.key = key;
        changeLog.oldValue = config.value;
        changeLog.newValue = value;
        changeLog.operatorId = typeof userId === 'number' ? userId : 0;
        changeLog.operatorName = typeof userName === 'string' ? userName : 'system';
        await logRepo.save(changeLog);
        
        // 更新配置
        config.value = value;
        if (description) config.description = description;
        await configRepo.save(config);
      } else {
        // 创建新配置
        config = new Config();
        config.type = type;
        config.key = key;
        config.value = value || '';
        config.description = description || key;
        await configRepo.save(config);
        
        // 记录创建日志
        const changeLog = new ConfigChangeLog();
        changeLog.type = type;
        changeLog.key = key;
        changeLog.oldValue = '';
        changeLog.newValue = value || '';
        changeLog.operatorId = typeof userId === 'number' ? userId : 0;
        changeLog.operatorName = typeof userName === 'string' ? userName : 'system';
        await logRepo.save(changeLog);
      }
    }
    
    return res.json({
      success: true,
      message: '配置更新成功'
    });
  } catch (error: any) {
    console.error('更新配置失败:', error);
    return res.status(500).json({
      success: false,
      message: '更新配置失败',
      error: error.message
    });
  }
};

/**
 * 重置配置
 */
export const resetConfig = async (req: Request, res: Response) => {
  try {
    const { type } = req.params;
    
    if (!type) {
      return res.status(400).json({
        success: false,
        message: '配置类型不能为空'
      });
    }
    
    const configRepo = AppDataSource.getRepository(Config);
    const logRepo = AppDataSource.getRepository(ConfigChangeLog);
    
    // 获取当前用户
    const userId = req.user?.id || 0; // 默认使用0代替null
    const userName = req.user?.username || 'system';
    
    // 获取所有该类型的配置
    const configs = await configRepo.find({ 
      where: { 
        type: type 
      } 
    });
    
    // 记录变更日志并删除配置
    for (const config of configs) {
      const changeLog = new ConfigChangeLog();
      changeLog.type = type;
      changeLog.key = config.key;
      changeLog.oldValue = config.value;
      changeLog.newValue = '';
      changeLog.operatorId = typeof userId === 'number' ? userId : 0;
      changeLog.operatorName = typeof userName === 'string' ? userName : 'system';
      await logRepo.save(changeLog);
    }
    
    // 删除所有该类型的配置
    if (configs.length > 0) {
      await configRepo.remove(configs);
    }
    
    return res.json({
      success: true,
      message: '配置已重置'
    });
  } catch (error: any) {
    console.error('重置配置失败:', error);
    return res.status(500).json({
      success: false,
      message: '重置配置失败',
      error: error.message
    });
  }
};

/**
 * 清除系统缓存
 */
export const clearCache = async (req: Request, res: Response) => {
  try {
    // 这里可以添加清除缓存的逻辑
    console.log('系统缓存清除请求已接收');
    
    // 模拟清除缓存操作
    await new Promise(resolve => setTimeout(resolve, 500));
    
    return res.json({
      success: true,
      message: '系统缓存已清除'
    });
  } catch (error: any) {
    console.error('清除缓存失败:', error);
    return res.status(500).json({
      success: false,
      message: '清除缓存失败',
      error: error.message
    });
  }
};

/**
 * 保存微信小程序配置
 * @param req - 请求对象
 * @param res - 响应对象
 * @returns 响应对象
 */
export const saveWechatMiniprogramConfig = async (req: Request, res: Response): Promise<Response> => {
  try {
    if (!req.body) {
      return res.status(400).json({ message: '请求体不能为空' });
    }

    const { appId, appSecret, enabled } = req.body;

    // 创建事务
    const queryRunner = AppDataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const configRepository = queryRunner.manager.getRepository(Config);

      // 处理appId
      await configRepository.delete({
        type: 'wechat-miniprogram',
        key: 'appId'
      });
      
      const appIdConfig = new Config();
      appIdConfig.type = 'wechat-miniprogram';
      appIdConfig.key = 'appId';
      appIdConfig.value = appId || '';
      appIdConfig.description = '微信小程序AppID';
      await configRepository.save(appIdConfig);

      // 处理appSecret
      await configRepository.delete({
        type: 'wechat-miniprogram',
        key: 'appSecret'
      });
      
      const appSecretConfig = new Config();
      appSecretConfig.type = 'wechat-miniprogram';
      appSecretConfig.key = 'appSecret';
      appSecretConfig.value = appSecret || '';
      appSecretConfig.description = '微信小程序AppSecret';
      await configRepository.save(appSecretConfig);

      // 处理enabled
      await configRepository.delete({
        type: 'wechat-miniprogram',
        key: 'enabled'
      });
      
      const enabledConfig = new Config();
      enabledConfig.type = 'wechat-miniprogram';
      enabledConfig.key = 'enabled';
      enabledConfig.value = enabled ? 'true' : 'false';
      enabledConfig.description = '微信小程序是否启用';
      await configRepository.save(enabledConfig);

      await queryRunner.commitTransaction();

      return res.status(200).json({
        code: 200,
        message: '微信小程序配置保存成功',
        data: { appId, appSecret, enabled }
      });
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  } catch (error) {
    console.error('保存微信小程序配置失败:', error);
    return res.status(500).json({
      code: 500,
      message: '保存微信小程序配置失败',
      error: error instanceof Error ? error.message : String(error)
    });
  }
};

/**
 * 获取推广配置
 */
export const getReferralConfig = async (req: Request, res: Response) => {
  try {
    const configRepo = AppDataSource.getRepository(Config);

    // 获取推广相关的配置
    const referralConfigs = await configRepo.find({
      where: {
        type: 'referral'
      }
    });

    // 转换为键值对格式
    const configData: any = {};
    referralConfigs.forEach(config => {
      configData[config.key] = config.value;
    });

    // 设置默认值
    const defaultConfig = {
      poster_background_image: '/images/banners/banner1.jpg',
      poster_qr_code_size: '80',
      poster_qr_code_position_x: '200',
      poster_qr_code_position_y: '300',
      share_title: 'AI智能体使用邀请',
      share_description: '注册即可获得额外免费使用次数，快来体验智能AI助手！',
      ...configData
    };

    console.log('获取推广配置成功');

    return res.status(200).json({
      success: true,
      data: defaultConfig
    });
  } catch (error) {
    console.error('获取推广配置失败', error);
    return res.status(500).json({
      success: false,
      message: '获取推广配置失败',
      error: error instanceof Error ? error.message : String(error)
    });
  }
};

/**
 * 生成邀请码 - 支持数据库缓存
 */
export const generateInviteCode = async (req: Request, res: Response) => {
  try {
    // 尝试从认证中间件获取用户ID
    let userId = (req as any).user?.id;

    // 如果没有通过认证中间件，尝试从Authorization头解析
    if (!userId) {
      const authHeader = req.headers.authorization;
      if (authHeader && authHeader.startsWith('Bearer ')) {
        const token = authHeader.substring(7);
        try {
          // 使用JWT解析用户ID
          const jwt = require('jsonwebtoken');
          const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
          const decoded = jwt.verify(token, JWT_SECRET) as any;
          userId = decoded.id;
          console.log('从JWT token解析出用户ID:', userId);
        } catch (jwtError) {
          console.error('JWT解析失败:', jwtError);
          return res.status(401).json({
            success: false,
            message: '无效的访问令牌'
          });
        }
      }
    }

    // 验证用户ID
    if (!userId || isNaN(Number(userId)) || Number(userId) <= 0) {
      console.error('无效的用户ID:', userId);
      return res.status(401).json({
        success: false,
        message: '用户未登录或用户ID无效'
      });
    }

    // 确保userId是数字类型
    userId = Number(userId);

    // 首先检查该用户是否已有邀请码
    try {
      const memberRepo = AppDataSource.getRepository(Member);
      const existingMember = await memberRepo.findOne({
        where: { id: userId }
      });

      if (existingMember && existingMember.inviteCode) {
        console.log(`用户[${userId}]使用已存在的邀请码: ${existingMember.inviteCode}`);
        return res.status(200).json({
          success: true,
          data: {
            inviteCode: existingMember.inviteCode,
            inviteUrl: existingMember.inviteUrl,
            inviter: userId,
            cached: true
          }
        });
      }
    } catch (dbError) {
      console.warn('查询已存在邀请码失败，继续生成新的:', dbError);
    }

    // 生成唯一邀请码
    const timestamp = Date.now().toString();
    const randomStr = Math.random().toString(36).substring(2, 8);
    const inviteCode = `INV${userId}${timestamp.slice(-6)}${randomStr.toUpperCase()}`;

    // 生成邀请链接
    const baseUrl = process.env.NODE_ENV === 'production'
      ? `https://${process.env.FRONTEND_DOMAIN || 'localhost'}`
      : 'http://localhost:3000';

    const inviteUrl = `${baseUrl}/#/pages/auth/register?inviteCode=${inviteCode}&inviter=${userId}`;

    // 保存到用户表
    try {
      const memberRepo = AppDataSource.getRepository(Member);
      console.log(`尝试保存邀请码到数据库，用户ID: ${userId}`);
      const updateResult = await memberRepo.update(userId, {
        inviteCode: inviteCode,
        inviteUrl: inviteUrl
      });
      console.log(`数据库更新结果:`, updateResult);
      console.log(`用户[${userId}]邀请码已保存到数据库: ${inviteCode}`);
    } catch (dbError) {
      console.error('保存邀请码到数据库失败:', dbError);
      console.error('错误详情:', dbError instanceof Error ? dbError.message : String(dbError));
    }

    console.log(`用户[${userId}]生成新邀请码: ${inviteCode}`);

    return res.status(200).json({
      success: true,
      data: {
        inviteCode,
        inviteUrl,
        inviter: userId,
        cached: false
      }
    });
  } catch (error) {
    console.error('生成邀请码失败', error);
    return res.status(500).json({
      success: false,
      message: '生成邀请码失败',
      error: error instanceof Error ? error.message : String(error)
    });
  }
};

/**
 * 保存海报图片
 */
export const savePoster = async (req: Request, res: Response) => {
  try {
    // 尝试从认证中间件获取用户ID
    let userId = (req as any).user?.id;

    // 如果没有通过认证中间件，尝试从Authorization头解析
    if (!userId) {
      const authHeader = req.headers.authorization;
      if (authHeader && authHeader.startsWith('Bearer ')) {
        const token = authHeader.substring(7);
        try {
          // 使用JWT解析用户ID
          const jwt = require('jsonwebtoken');
          const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
          const decoded = jwt.verify(token, JWT_SECRET) as any;
          userId = decoded.id;
          console.log('从JWT token解析出用户ID:', userId);
        } catch (jwtError) {
          console.error('JWT解析失败:', jwtError);
          return res.status(401).json({
            success: false,
            message: '无效的访问令牌'
          });
        }
      }
    }

    const { imageData, inviteCode } = req.body;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '用户未登录'
      });
    }

    if (!imageData || !inviteCode) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数'
      });
    }

    // 这里可以添加保存图片到服务器的逻辑
    // 目前只返回成功响应
    console.log(`用户[${userId}]保存海报，邀请码: ${inviteCode}`);

    return res.status(200).json({
      success: true,
      message: '海报保存成功',
      data: {
        inviteCode,
        userId
      }
    });
  } catch (error) {
    console.error('保存海报失败', error);
    return res.status(500).json({
      success: false,
      message: '保存海报失败',
      error: error instanceof Error ? error.message : String(error)
    });
  }
};

/**
 * 生成二维码图片文件
 */
export const generateQRCode = async (req: Request, res: Response) => {
  try {
    // 尝试从认证中间件获取用户ID
    let userId = (req as any).user?.id;

    // 如果没有通过认证中间件，尝试从Authorization头解析
    if (!userId) {
      const authHeader = req.headers.authorization;
      if (authHeader && authHeader.startsWith('Bearer ')) {
        const token = authHeader.substring(7);
        try {
          // 使用JWT解析用户ID
          const jwt = require('jsonwebtoken');
          const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
          const decoded = jwt.verify(token, JWT_SECRET) as any;
          userId = decoded.id;
          console.log('从JWT token解析出用户ID:', userId);
        } catch (jwtError) {
          console.error('JWT解析失败:', jwtError);
          return res.status(401).json({
            success: false,
            message: '无效的访问令牌'
          });
        }
      }
    }

    const { inviteCode, size = 200 } = req.body;

    // 验证用户ID
    if (!userId || isNaN(Number(userId)) || Number(userId) <= 0) {
      console.error('无效的用户ID:', userId);
      return res.status(401).json({
        success: false,
        message: '用户未登录或用户ID无效'
      });
    }

    // 确保userId是数字类型
    userId = Number(userId);

    if (!inviteCode) {
      return res.status(400).json({
        success: false,
        message: '缺少邀请码参数'
      });
    }

    // 生成邀请链接
    const baseUrl = process.env.NODE_ENV === 'production'
      ? `https://${process.env.FRONTEND_DOMAIN || 'localhost'}`
      : 'http://localhost:3000';

    const inviteUrl = `${baseUrl}/#/pages/auth/register?inviteCode=${inviteCode}`;

    // 使用qrcode库生成二维码
    const QRCode = require('qrcode');
    const fs = require('fs');
    const path = require('path');

    // 创建保存目录 - 保存到项目根目录的public/images/qrcodes
    const uploadsDir = path.join(__dirname, '../../../public/images/qrcodes');
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir, { recursive: true });
    }

    // 首先检查该用户是否已有二维码
    try {
      const memberRepo = AppDataSource.getRepository(Member);
      const existingMember = await memberRepo.findOne({
        where: { id: userId }
      });

      if (existingMember && existingMember.qrCodePath && existingMember.inviteCode === inviteCode) {
        // 检查文件是否仍然存在
        const fullPath = path.join(__dirname, '../../../public', existingMember.qrCodePath);
        if (fs.existsSync(fullPath)) {
          console.log(`用户[${userId}]使用数据库缓存的二维码: ${existingMember.qrCodePath}`);
          return res.status(200).json({
            success: true,
            data: {
              qrCodePath: existingMember.qrCodePath,
              inviteUrl: existingMember.inviteUrl,
              inviteCode: existingMember.inviteCode,
              cached: true
            }
          });
        }
      }
    } catch (dbError) {
      console.warn('查询数据库二维码缓存失败:', dbError);
    }

    // 检查是否已存在该用户的二维码文件（文件系统缓存机制）
    const cacheFileName = `qrcode_${userId}_${inviteCode}.png`;
    const cacheFilePath = path.join(uploadsDir, cacheFileName);

    let fileName = cacheFileName;
    let filePath = cacheFilePath;

    // 如果缓存文件存在且是24小时内创建的，直接返回
    if (fs.existsSync(cacheFilePath)) {
      const stats = fs.statSync(cacheFilePath);
      const fileAge = Date.now() - stats.mtime.getTime();
      const cacheExpiry = 24 * 60 * 60 * 1000; // 24小时

      if (fileAge < cacheExpiry) {
        console.log(`用户[${userId}]使用文件系统缓存的二维码: ${cacheFileName}`);
        const qrCodePath = `/images/qrcodes/${cacheFileName}`;

        // 同时更新数据库缓存
        try {
          const memberRepo = AppDataSource.getRepository(Member);
          await memberRepo.update(userId, {
            qrCodePath: qrCodePath,
            qrCodeUrl: `${baseUrl}${qrCodePath}`
          });
        } catch (updateError) {
          console.warn('更新数据库二维码缓存失败:', updateError);
        }

        return res.status(200).json({
          success: true,
          data: {
            qrCodePath,
            inviteUrl,
            inviteCode,
            cached: true
          }
        });
      } else {
        // 缓存过期，删除旧文件
        try {
          fs.unlinkSync(cacheFilePath);
          console.log(`删除过期的二维码缓存: ${cacheFileName}`);
        } catch (deleteError) {
          console.warn('删除过期缓存文件失败:', deleteError);
        }
      }
    }

    // 生成新的二维码图片
    await QRCode.toFile(filePath, inviteUrl, {
      width: size,
      margin: 2,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      }
    });

    // 返回相对路径 - 前端会通过 IMAGE_BASE_URL + 这个路径来访问
    const qrCodePath = `/images/qrcodes/${fileName}`;
    const qrCodeUrl = `${baseUrl}${qrCodePath}`;

    // 保存到用户表
    try {
      const memberRepo = AppDataSource.getRepository(Member);
      console.log(`尝试保存二维码信息到数据库，用户ID: ${userId}`);
      const updateResult = await memberRepo.update(userId, {
        qrCodePath: qrCodePath,
        qrCodeUrl: qrCodeUrl
      });
      console.log(`二维码数据库更新结果:`, updateResult);
      console.log(`用户[${userId}]二维码信息已保存到数据库`);
    } catch (dbError) {
      console.error('保存二维码信息到数据库失败:', dbError);
      console.error('错误详情:', dbError instanceof Error ? dbError.message : String(dbError));
    }

    console.log(`用户[${userId}]生成新二维码: ${fileName}`);

    return res.status(200).json({
      success: true,
      data: {
        qrCodePath,
        inviteUrl,
        inviteCode,
        cached: false
      }
    });
  } catch (error) {
    console.error('生成二维码失败', error);
    return res.status(500).json({
      success: false,
      message: '生成二维码失败',
      error: error instanceof Error ? error.message : String(error)
    });
  }
};