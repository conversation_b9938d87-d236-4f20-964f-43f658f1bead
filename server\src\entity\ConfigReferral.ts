/**
 * 推荐奖励配置实体类
 */
import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('config_referral')
export class ConfigReferral {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'tinyint', width: 1, default: 0, comment: '是否启用推荐奖励' })
  enabled: boolean;

  // 海报配置
  @Column({ name: 'poster_background_image', length: 500, default: '', comment: '海报背景图片URL' })
  posterBackgroundImage: string;

  @Column({ name: 'poster_qr_code_size', type: 'int', default: 202, comment: '二维码大小（像素）' })
  posterQrCodeSize: number;

  @Column({ name: 'poster_qr_code_position_x', type: 'int', default: 200, comment: '二维码X坐标位置' })
  posterQrCodePositionX: number;

  @Column({ name: 'poster_qr_code_position_y', type: 'int', default: 400, comment: '二维码Y坐标位置' })
  posterQrCodePositionY: number;

  // 分享配置
  @Column({ name: 'share_title', length: 200, default: 'AI智能体使用邀请', comment: '分享标题' })
  shareTitle: string;

  @Column({ name: 'share_description', length: 500, default: '注册即可获得额外免费使用次数，快来体验智能AI助手！', comment: '分享描述' })
  shareDescription: string;

  @Column({ name: 'share_image_url', length: 500, default: '', comment: '分享图片URL' })
  shareImageUrl: string;

  // 奖励配置
  @Column({ name: 'invite_reward_points', type: 'int', default: 100, comment: '邀请奖励点数' })
  inviteRewardPoints: number;

  @Column({ name: 'register_reward_points', type: 'int', default: 50, comment: '注册奖励点数' })
  registerRewardPoints: number;

  // 邀请配置
  @Column({ name: 'inviter_reward', type: 'int', default: 50, comment: '邀请人奖励' })
  inviterReward: number;

  @Column({ name: 'max_invite_count', type: 'int', default: 10, comment: '最大邀请次数' })
  maxInviteCount: number;

  @Column({ name: 'reward_valid_days', type: 'int', default: 60, comment: '奖励有效期（天）' })
  rewardValidDays: number;

  @Column({ name: 'invite_success_message', length: 500, default: '恭喜您成功邀请好友，获得{reward}次免费使用奖励！', comment: '邀请成功提示信息' })
  inviteSuccessMessage: string;

  // 合伙人配置
  @Column({ name: 'invitee_reward', type: 'int', default: 30, comment: '被邀请人奖励' })
  inviteeReward: number;

  @Column({ name: 'promoter_cashback', type: 'decimal', precision: 5, scale: 2, default: 5.00, comment: '推广人返现比例（%）' })
  promoterCashback: number;

  @Column({ name: 'promoter_commission', type: 'decimal', precision: 5, scale: 2, default: 2.00, comment: '推广人佣金比例（%）' })
  promoterCommission: number;

  @Column({ name: 'min_withdrawal', type: 'decimal', precision: 10, scale: 2, default: 1.00, comment: '最低提现金额' })
  minWithdrawal: number;

  @Column({ name: 'withdrawal_channel', length: 20, default: 'alipay', comment: '提现渠道' })
  withdrawalChannel: string;

  @Column({ name: 'withdrawal_fee', type: 'decimal', precision: 5, scale: 2, default: 0.00, comment: '提现手续费（%）' })
  withdrawalFee: number;

  @CreateDateColumn({ name: 'created_at', comment: '创建时间' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', comment: '更新时间' })
  updatedAt: Date;
}
