<template>
  <n-modal
    v-model:show="showModal"
    preset="card"
    title="生成推广海报"
    style="width: 900px; max-width: 95vw"
    :segmented="{ content: true }"
    :closable="true"
    :mask-closable="false"
  >
    <div class="poster-generator">
      <!-- 加载状态 -->
      <div v-if="generating" class="loading-container">
        <n-spin size="large">
          <template #description>
            <div class="loading-text">正在生成推广海报...</div>
          </template>
        </n-spin>
      </div>

      <!-- 海报预览和操作 -->
      <div v-else class="poster-content">
        <div class="poster-preview-section">
          <div class="poster-title">
            <n-icon size="20" color="#18a058">
              <QrCodeOutline />
            </n-icon>
            <span>推广海报预览</span>
          </div>
          
          <div class="poster-container">
            <canvas
              ref="posterCanvas"
              :width="canvasWidth"
              :height="canvasHeight"
              class="poster-canvas"
            ></canvas>
          </div>
        </div>

        <div class="poster-actions">
          <div class="action-buttons">
            <n-button
              type="primary"
              size="large"
              @click="downloadPoster"
              :loading="downloading"
              class="action-btn download-btn"
            >
              <template #icon>
                <n-icon><DownloadOutline /></n-icon>
              </template>
              保存到本地
            </n-button>

            <n-button
              type="success"
              size="large"
              @click="sharePoster"
              :loading="sharing"
              class="action-btn share-btn"
            >
              <template #icon>
                <n-icon><ShareOutline /></n-icon>
              </template>
              分享海报
            </n-button>

            <n-button
              type="info"
              size="large"
              @click="regeneratePoster"
              :loading="generating"
              class="action-btn regenerate-btn"
            >
              <template #icon>
                <n-icon><RefreshOutline /></n-icon>
              </template>
              重新生成
            </n-button>
          </div>

          <div class="poster-info">
            <n-alert type="info" :show-icon="false" class="info-alert">
              <div class="info-content">
                <div class="info-item">
                  <span class="info-label">邀请码:</span>
                  <span class="info-value">{{ inviteCode }}</span>
                  <n-button text @click="copyInviteCode" class="copy-btn">
                    <n-icon><CopyOutline /></n-icon>
                  </n-button>
                </div>
                <div class="info-item">
                  <span class="info-label">二维码:</span>
                  <span class="info-value">包含用户专属邀请链接</span>
                </div>
              </div>
            </n-alert>
          </div>
        </div>
      </div>
    </div>

    <template #action>
      <div class="modal-actions">
        <n-button @click="closeModal" class="close-btn">关闭</n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, watch, nextTick, onMounted } from 'vue'
import { NModal, NButton, NIcon, NSpin, NAlert, useMessage } from 'naive-ui'
import {
  QrCodeOutline,
  DownloadOutline,
  ShareOutline,
  RefreshOutline,
  CopyOutline
} from '@vicons/ionicons5'
import QRCode from 'qrcode'
import { getCurrentUser } from '@/api/user'
import axios from 'axios'

interface Props {
  show: boolean
  posterConfig?: {
    backgroundImage: string
    qrCodeSize: number
    qrCodePosition: { x: number; y: number }
  }
  shareConfig?: {
    title: string
    description: string
  }
}

interface Emits {
  (e: 'update:show', value: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
  posterConfig: () => ({
    backgroundImage: '',
    qrCodeSize: 202,
    qrCodePosition: { x: 50, y: 50 }
  }),
  shareConfig: () => ({
    title: 'AI智能体使用邀请',
    description: '注册即可获得额外免费使用次数，快来体验智能AI助手！'
  })
})

const emit = defineEmits<Emits>()
const message = useMessage()

// 响应式数据
const showModal = ref(false)
const generating = ref(false)
const downloading = ref(false)
const sharing = ref(false)
const posterCanvas = ref<HTMLCanvasElement>()
const inviteCode = ref('')
const userInfo = ref<any>({})

// 画布尺寸
const canvasWidth = 600
const canvasHeight = 800

// 监听显示状态
watch(() => props.show, (newVal) => {
  showModal.value = newVal
  if (newVal) {
    initPosterGeneration()
  }
})

watch(showModal, (newVal) => {
  emit('update:show', newVal)
})

// 初始化海报生成
const initPosterGeneration = async () => {
  try {
    generating.value = true
    
    // 获取用户信息
    await fetchUserInfo()
    
    // 生成邀请码
    await generateInviteCode()
    
    // 生成海报
    await generatePoster()
    
  } catch (error) {
    console.error('初始化海报生成失败:', error)
    message.error('生成海报失败，请重试')
  } finally {
    generating.value = false
  }
}

// 获取用户信息
const fetchUserInfo = async () => {
  try {
    const response = await getCurrentUser()
    if (response && response.data) {
      userInfo.value = response.data
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
  }
}

// 生成邀请码
const generateInviteCode = async () => {
  try {
    const response = await axios.post('/api/config/generate-invite-code', {}, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
      }
    })

    if (response.data && response.data.success) {
      inviteCode.value = response.data.data.inviteCode
    } else {
      throw new Error(response.data?.message || '生成邀请码失败')
    }
  } catch (error) {
    console.error('生成邀请码失败:', error)
    // 降级到本地生成
    const userId = userInfo.value.id || 'user123'
    inviteCode.value = `INV${userId}${Date.now().toString().slice(-6)}`
  }
}

// 生成海报
const generatePoster = async () => {
  if (!posterCanvas.value) return

  const canvas = posterCanvas.value
  const ctx = canvas.getContext('2d')
  if (!ctx) return

  try {
    // 清空画布
    ctx.clearRect(0, 0, canvasWidth, canvasHeight)

    // 绘制背景
    await drawBackground(ctx)

    // 绘制内容
    await drawContent(ctx)

    // 绘制二维码
    await drawQRCode(ctx)

  } catch (error) {
    console.error('生成海报失败:', error)
    throw error
  }
}

// 绘制背景
const drawBackground = async (ctx: CanvasRenderingContext2D) => {
  if (props.posterConfig.backgroundImage) {
    try {
      const img = new Image()
      img.crossOrigin = 'anonymous'
      
      await new Promise((resolve, reject) => {
        img.onload = resolve
        img.onerror = reject
        img.src = props.posterConfig.backgroundImage
      })

      ctx.drawImage(img, 0, 0, canvasWidth, canvasHeight)
    } catch (error) {
      console.error('加载背景图片失败:', error)
      // 使用默认渐变背景
      drawDefaultBackground(ctx)
    }
  } else {
    drawDefaultBackground(ctx)
  }
}

// 绘制默认背景
const drawDefaultBackground = (ctx: CanvasRenderingContext2D) => {
  const gradient = ctx.createLinearGradient(0, 0, 0, canvasHeight)
  gradient.addColorStop(0, '#667eea')
  gradient.addColorStop(1, '#764ba2')
  
  ctx.fillStyle = gradient
  ctx.fillRect(0, 0, canvasWidth, canvasHeight)
}

// 绘制内容
const drawContent = async (ctx: CanvasRenderingContext2D) => {
  // 绘制标题
  ctx.fillStyle = '#ffffff'
  ctx.font = 'bold 36px Arial, sans-serif'
  ctx.textAlign = 'center'
  ctx.fillText(props.shareConfig.title, canvasWidth / 2, 100)

  // 绘制描述
  ctx.font = '24px Arial, sans-serif'
  ctx.fillStyle = '#f0f0f0'
  
  const description = props.shareConfig.description
  const maxWidth = canvasWidth - 80
  const lineHeight = 35
  const words = description.split('')
  let line = ''
  let y = 160

  for (let i = 0; i < words.length; i++) {
    const testLine = line + words[i]
    const metrics = ctx.measureText(testLine)
    
    if (metrics.width > maxWidth && line !== '') {
      ctx.fillText(line, canvasWidth / 2, y)
      line = words[i]
      y += lineHeight
    } else {
      line = testLine
    }
  }
  ctx.fillText(line, canvasWidth / 2, y)

  // 绘制用户信息
  if (userInfo.value.nickname || userInfo.value.username) {
    ctx.font = '20px Arial, sans-serif'
    ctx.fillStyle = '#ffffff'
    ctx.fillText(
      `邀请人: ${userInfo.value.nickname || userInfo.value.username}`,
      canvasWidth / 2,
      canvasHeight - 100
    )
  }

  // 绘制邀请码
  ctx.font = '18px Arial, sans-serif'
  ctx.fillStyle = '#ffeb3b'
  ctx.fillText(`邀请码: ${inviteCode.value}`, canvasWidth / 2, canvasHeight - 60)
}

// 绘制二维码
const drawQRCode = async (ctx: CanvasRenderingContext2D) => {
  try {
    // 生成邀请链接
    const inviteUrl = `${window.location.origin}?inviteCode=${inviteCode.value}&inviter=${userInfo.value.id}`
    
    // 生成二维码
    const qrCodeDataUrl = await QRCode.toDataURL(inviteUrl, {
      width: props.posterConfig.qrCodeSize,
      margin: 1,
      color: {
        dark: '#000000',
        light: '#ffffff'
      }
    })

    const qrImg = new Image()
    await new Promise((resolve, reject) => {
      qrImg.onload = resolve
      qrImg.onerror = reject
      qrImg.src = qrCodeDataUrl
    })

    // 绘制二维码
    const qrX = props.posterConfig.qrCodePosition.x
    const qrY = props.posterConfig.qrCodePosition.y
    const qrSize = props.posterConfig.qrCodeSize

    // 绘制白色背景
    ctx.fillStyle = '#ffffff'
    ctx.fillRect(qrX - 10, qrY - 10, qrSize + 20, qrSize + 20)

    // 绘制二维码
    ctx.drawImage(qrImg, qrX, qrY, qrSize, qrSize)

  } catch (error) {
    console.error('生成二维码失败:', error)
    
    // 绘制占位符
    ctx.fillStyle = '#ffffff'
    ctx.fillRect(
      props.posterConfig.qrCodePosition.x,
      props.posterConfig.qrCodePosition.y,
      props.posterConfig.qrCodeSize,
      props.posterConfig.qrCodeSize
    )
    
    ctx.fillStyle = '#666666'
    ctx.font = '16px Arial, sans-serif'
    ctx.textAlign = 'center'
    ctx.fillText(
      '二维码',
      props.posterConfig.qrCodePosition.x + props.posterConfig.qrCodeSize / 2,
      props.posterConfig.qrCodePosition.y + props.posterConfig.qrCodeSize / 2
    )
  }
}

// 下载海报
const downloadPoster = async () => {
  if (!posterCanvas.value) return

  try {
    downloading.value = true
    
    const canvas = posterCanvas.value
    const link = document.createElement('a')
    link.download = `推广海报_${inviteCode.value}_${new Date().getTime()}.png`
    link.href = canvas.toDataURL('image/png')
    link.click()
    
    message.success('海报已保存到本地')
  } catch (error) {
    console.error('下载海报失败:', error)
    message.error('下载失败，请重试')
  } finally {
    downloading.value = false
  }
}

// 分享海报
const sharePoster = async () => {
  try {
    sharing.value = true
    
    if (navigator.share && posterCanvas.value) {
      // 使用 Web Share API
      const canvas = posterCanvas.value
      canvas.toBlob(async (blob) => {
        if (blob) {
          const file = new File([blob], `推广海报_${inviteCode.value}.png`, { type: 'image/png' })
          
          try {
            await navigator.share({
              title: props.shareConfig.title,
              text: props.shareConfig.description,
              files: [file]
            })
            message.success('分享成功')
          } catch (error) {
            console.error('分享失败:', error)
            // 降级到复制链接
            await copyInviteLink()
          }
        }
      }, 'image/png')
    } else {
      // 降级到复制邀请链接
      await copyInviteLink()
    }
  } catch (error) {
    console.error('分享失败:', error)
    message.error('分享失败，请重试')
  } finally {
    sharing.value = false
  }
}

// 复制邀请链接
const copyInviteLink = async () => {
  try {
    const inviteUrl = `${window.location.origin}?inviteCode=${inviteCode.value}&inviter=${userInfo.value.id}`
    await navigator.clipboard.writeText(inviteUrl)
    message.success('邀请链接已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    message.error('复制失败，请手动复制')
  }
}

// 复制邀请码
const copyInviteCode = async () => {
  try {
    await navigator.clipboard.writeText(inviteCode.value)
    message.success('邀请码已复制到剪贴板')
  } catch (error) {
    console.error('复制邀请码失败:', error)
    message.error('复制失败，请手动复制')
  }
}

// 重新生成海报
const regeneratePoster = async () => {
  await initPosterGeneration()
  message.success('海报已重新生成')
}

// 关闭弹窗
const closeModal = () => {
  showModal.value = false
}
</script>

<style scoped>
.poster-generator {
  min-height: 500px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}

.loading-text {
  margin-top: 16px;
  font-size: 16px;
  color: #666;
}

.poster-content {
  display: flex;
  gap: 24px;
}

.poster-preview-section {
  flex: 1;
}

.poster-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.poster-container {
  display: flex;
  justify-content: center;
  padding: 20px;
  background: #f5f5f5;
  border-radius: 8px;
  border: 2px dashed #d9d9d9;
}

.poster-canvas {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.poster-actions {
  width: 280px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.action-btn {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
}

.download-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

.share-btn {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  border: none;
}

.regenerate-btn {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  border: none;
}

.poster-info {
  margin-top: 8px;
}

.info-alert {
  border-radius: 8px;
}

.info-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-label {
  font-weight: 600;
  color: #666;
  min-width: 60px;
}

.info-value {
  flex: 1;
  color: #333;
  font-size: 14px;
}

.copy-btn {
  padding: 4px;
  color: #18a058;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
}

.close-btn {
  min-width: 80px;
}

@media (max-width: 768px) {
  .poster-content {
    flex-direction: column;
  }
  
  .poster-actions {
    width: 100%;
  }
  
  .action-buttons {
    flex-direction: row;
    flex-wrap: wrap;
  }
  
  .action-btn {
    flex: 1;
    min-width: 120px;
  }
}
</style>
